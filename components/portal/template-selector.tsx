// components/portal/template-selector.tsx
'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Code, 
  Palette, 
  Users, 
  BarChart3, 
  Briefcase,
  Camera,
  PenTool,
  Check
} from 'lucide-react'
import { DEFAULT_TEMPLATES } from '@/lib/constants'
import { Template } from '@/lib/types'

interface TemplateSelectorProps {
  selectedTemplate: Template | null
  onSelect: (template: Template) => void
  projectData: {
    name: string
    clientName: string
    clientEmail: string
    description: string
  }
  onProjectDataChange: (data: any) => void
}

const categoryIcons = {
  DEVELOPMENT: Code,
  DESIGN: Palette,
  COACHING: Users,
  MARKETING: BarChart3,
  CONSULTING: Briefcase,
  PHOTOGRAPHY: Camera,
  WRITING: PenTool,
}

export function TemplateSelector({
  selectedTemplate,
  onSelect,
  projectData,
  onProjectDataChange
}: TemplateSelectorProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [templates, setTemplates] = useState<Template[]>([])

  useEffect(() => {
    // Load templates (in a real app, this would be an API call)
    setTemplates(DEFAULT_TEMPLATES as any)
  }, [])

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const categories = Array.from(new Set(templates.map(t => t.category)))

  const handleProjectDataChange = (field: string, value: string) => {
    onProjectDataChange({ [field]: value })
  }

  return (
    <div className="space-y-6">
      {/* Project Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Project Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="projectName">Project Name *</Label>
              <Input
                id="projectName"
                placeholder="e.g., Website Redesign Project"
                value={projectData.name}
                onChange={(e) => handleProjectDataChange('projectName', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="clientName">Client Name *</Label>
              <Input
                id="clientName"
                placeholder="e.g., John Smith"
                value={projectData.clientName}
                onChange={(e) => handleProjectDataChange('clientName', e.target.value)}
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="clientEmail">Client Email *</Label>
            <Input
              id="clientEmail"
              type="email"
              placeholder="e.g., <EMAIL>"
              value={projectData.clientEmail}
              onChange={(e) => handleProjectDataChange('clientEmail', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">Project Description</Label>
            <Textarea
              id="description"
              placeholder="Brief description of the project..."
              value={projectData.description}
              onChange={(e) => handleProjectDataChange('description', e.target.value)}
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Template Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Choose Template</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>
                  {category.charAt(0) + category.slice(1).toLowerCase()}
                </option>
              ))}
            </select>
          </div>

          {/* Templates Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTemplates.map((template) => {
              const IconComponent = categoryIcons[template.category as keyof typeof categoryIcons] || Briefcase
              const isSelected = selectedTemplate?.id === template.id

              return (
                <Card
                  key={template.id}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                  }`}
                  onClick={() => onSelect(template)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-gray-100 rounded-lg">
                          <IconComponent className="h-5 w-5 text-gray-600" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{template.name}</CardTitle>
                          <Badge variant="outline" className="mt-1">
                            {template.category.charAt(0) + template.category.slice(1).toLowerCase()}
                          </Badge>
                        </div>
                      </div>
                      {isSelected && (
                        <div className="p-1 bg-blue-500 rounded-full">
                          <Check className="h-4 w-4 text-white" />
                        </div>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600 mb-4">{template.description}</p>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{template.formFields?.length || 0} form fields</span>
                        <span>{template.checklistItems?.length || 0} checklist items</span>
                      </div>
                      {isSelected && (
                        <Button className="w-full" size="sm">
                          Selected
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {filteredTemplates.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-600">No templates found matching your criteria.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}