// components/portal/preview-modal.tsx
'use client'

import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { ExternalLink, Eye, Smartphone, Monitor } from 'lucide-react'
import { useState } from 'react'

interface PreviewModalProps {
  isOpen: boolean
  onClose: () => void
  portalData: {
    projectName: string
    clientName: string
    template?: any
    formFields: any[]
    checklistItems: any[]
    branding: {
      logoUrl?: string
      primaryColor: string
      secondaryColor: string
    }
  }
}

export function PreviewModal({ isOpen, onClose, portalData }: PreviewModalProps) {
  const [previewDevice, setPreviewDevice] = useState<'desktop' | 'mobile'>('desktop')

  const renderFormField = (field: any) => {
    switch (field.type) {
      case 'TEXT':
      case 'EMAIL':
      case 'PHONE':
        return (
          <Input
            placeholder={`Enter ${field.label.toLowerCase()}`}
            disabled
            className="opacity-75"
          />
        )
      case 'TEXTAREA':
        return (
          <Textarea
            placeholder={`Enter ${field.label.toLowerCase()}`}
            disabled
            rows={3}
            className="opacity-75"
          />
        )
      case 'SELECT':
        return (
          <select disabled className="w-full p-2 border rounded-md opacity-75 bg-gray-50">
            <option>Select an option...</option>
            {field.options?.map((option: string, index: number) => (
              <option key={index} value={option}>{option}</option>
            ))}
          </select>
        )
      case 'DATE':
        return (
          <Input
            type="date"
            disabled
            className="opacity-75"
          />
        )
      case 'FILE_UPLOAD':
        return (
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center opacity-75">
            <p className="text-sm text-gray-500">Click to upload files</p>
          </div>
        )
      default:
        return (
          <Input
            placeholder={`Enter ${field.label.toLowerCase()}`}
            disabled
            className="opacity-75"
          />
        )
    }
  }

  const containerClass = previewDevice === 'mobile' 
    ? 'max-w-sm mx-auto' 
    : 'max-w-4xl mx-auto'

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center space-x-2">
              <Eye className="h-5 w-5" />
              <span>Portal Preview</span>
            </DialogTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant={previewDevice === 'desktop' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPreviewDevice('desktop')}
              >
                <Monitor className="h-4 w-4 mr-1" />
                Desktop
              </Button>
              <Button
                variant={previewDevice === 'mobile' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPreviewDevice('mobile')}
              >
                <Smartphone className="h-4 w-4 mr-1" />
                Mobile
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className={`bg-gray-100 p-4 rounded-lg ${containerClass}`}>
          {/* Portal Header */}
          <div 
            className="bg-white rounded-t-lg p-6 border-b"
            style={{ 
              borderTopColor: portalData.branding.primaryColor,
              borderTopWidth: '4px'
            }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {portalData.branding.logoUrl && (
                  <img
                    src={portalData.branding.logoUrl}
                    alt="Logo"
                    className="h-12 w-auto"
                  />
                )}
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    Welcome, {portalData.clientName}!
                  </h1>
                  <p className="text-gray-600">
                    Complete your onboarding for: {portalData.projectName}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Portal Content */}
          <div className="bg-white p-6 space-y-8">
            {/* Form Fields Section */}
            {portalData.formFields.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Project Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {portalData.formFields.map((field, index) => (
                    <div key={index} className="space-y-2">
                      <label className="text-sm font-medium text-gray-700">
                        {field.label}
                        {field.required && <span className="text-red-500 ml-1">*</span>}
                      </label>
                      {renderFormField(field)}
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Checklist Section */}
            {portalData.checklistItems.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Onboarding Checklist</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {portalData.checklistItems.map((item, index) => (
                    <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                      <Checkbox disabled className="mt-1" />
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{item.title}</h4>
                        {item.description && (
                          <p className="text-sm text-gray-600 mt-1">{item.description}</p>
                        )}
                        <Badge 
                          variant="outline" 
                          className="mt-2"
                          style={{ 
                            color: item.assigneeType === 'CLIENT' 
                              ? portalData.branding.primaryColor 
                              : portalData.branding.secondaryColor 
                          }}
                        >
                          {item.assigneeType === 'CLIENT' ? 'Your Task' : 
                           item.assigneeType === 'FREELANCER' ? 'Provider Task' : 'Shared Task'}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button 
                className="flex-1"
                style={{ backgroundColor: portalData.branding.primaryColor }}
                disabled
              >
                Save Progress
              </Button>
              <Button 
                variant="outline" 
                className="flex-1"
                disabled
              >
                Submit for Review
              </Button>
            </div>
          </div>

          {/* Footer */}
          <div className="bg-gray-50 p-4 rounded-b-lg text-center">
            <p className="text-xs text-gray-500">
              Powered by Client Onboarding Assistant
            </p>
          </div>
        </div>

        <div className="flex justify-between items-center pt-4">
          <p className="text-sm text-gray-600">
            This is how your client will see their onboarding portal
          </p>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={onClose}>
              Close Preview
            </Button>
            <Button>
              <ExternalLink className="h-4 w-4 mr-2" />
              Share Portal
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}