// components/portal/portal-wizard.tsx
'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { TemplateSelector } from './template-selector'
import { FormBuilder } from './form-builder'
import { ChecklistBuilder } from './checklist-builder'
import { BrandingEditor } from './branding-editor'
import { PreviewModal } from './preview-modal'
import { 
  ChevronLeft, 
  ChevronRight, 
  Check, 
  Eye,
  Send,
  Save
} from 'lucide-react'
import { Template, FormField, ChecklistItem } from '@/lib/types'

interface WizardStep {
  id: string
  title: string
  description: string
  completed: boolean
}

interface PortalWizardProps {
  initialData?: any
  isEditing?: boolean
}

export function PortalWizard({ initialData, isEditing = false }: PortalWizardProps) {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const [showPreview, setShowPreview] = useState(false)

  const [wizardData, setWizardData] = useState({
    projectName: initialData?.name || '',
    clientName: initialData?.clientName || '',
    clientEmail: initialData?.clientEmail || '',
    description: initialData?.description || '',
    template: initialData?.template || null,
    formFields: initialData?.formFields || [],
    checklistItems: initialData?.checklistItems || [],
    branding: initialData?.branding || {
      logoUrl: '',
      primaryColor: '#2563eb',
      secondaryColor: '#64748b',
      customDomain: ''
    }
  })

  const steps: WizardStep[] = [
    {
      id: 'template',
      title: 'Choose Template',
      description: 'Select a template that matches your industry',
      completed: !!wizardData.template
    },
    {
      id: 'form',
      title: 'Build Form',
      description: 'Customize the information you need from clients',
      completed: wizardData.formFields.length > 0
    },
    {
      id: 'checklist',
      title: 'Create Checklist',
      description: 'Define the onboarding steps and tasks',
      completed: wizardData.checklistItems.length > 0
    },
    {
      id: 'branding',
      title: 'Customize Design',
      description: 'Add your logo and brand colors',
      completed: wizardData.branding.primaryColor !== '#2563eb'
    }
  ]

  const progress = ((currentStep + 1) / steps.length) * 100

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleStepClick = (stepIndex: number) => {
    setCurrentStep(stepIndex)
  }

  const handleTemplateSelect = (template: Template) => {
    setWizardData(prev => ({
      ...prev,
      template,
      formFields: template.formFields || [],
      checklistItems: template.checklistItems || []
    }))
  }

  const handleFormFieldsChange = (formFields: FormField[]) => {
    setWizardData(prev => ({ ...prev, formFields }))
  }

  const handleChecklistChange = (checklistItems: ChecklistItem[]) => {
    setWizardData(prev => ({ ...prev, checklistItems }))
  }

  const handleBrandingChange = (branding: any) => {
    setWizardData(prev => ({ ...prev, branding }))
  }

  const handleSave = async (isDraft = true) => {
    setIsLoading(true)
    try {
      const projectData = {
        ...wizardData,
        status: isDraft ? 'DRAFT' : 'SHARED'
      }

      const url = isEditing ? `/api/projects/${initialData?.id}` : '/api/projects'
      const method = isEditing ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(projectData)
      })

      if (!response.ok) throw new Error('Failed to save project')

      const result = await response.json()
      
      if (!isDraft) {
        // Send invitation email to client
        await fetch('/api/emails/send', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: 'client_invitation',
            to: wizardData.clientEmail,
            data: {
              clientName: wizardData.clientName,
              portalUrl: `${window.location.origin}/portal/${result.shareToken}`,
              freelancerName: 'Your Service Provider' // This should come from user session
            }
          })
        })
      }

      router.push('/dashboard')
    } catch (error) {
      console.error('Save error:', error)
      // Handle error (show toast, etc.)
    } finally {
      setIsLoading(false)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <TemplateSelector
            selectedTemplate={wizardData.template}
            onSelect={handleTemplateSelect}
            projectData={{
              name: wizardData.projectName,
              clientName: wizardData.clientName,
              clientEmail: wizardData.clientEmail,
              description: wizardData.description
            }}
            onProjectDataChange={(data) => setWizardData(prev => ({ ...prev, ...data }))}
          />
        )
      case 1:
        return (
          <FormBuilder
            fields={wizardData.formFields}
            onChange={handleFormFieldsChange}
          />
        )
      case 2:
        return (
          <ChecklistBuilder
            items={wizardData.checklistItems}
            onChange={handleChecklistChange}
          />
        )
      case 3:
        return (
          <BrandingEditor
            branding={wizardData.branding}
            onChange={handleBrandingChange}
          />
        )
      default:
        return null
    }
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {isEditing ? 'Edit Project' : 'Create New Project'}
        </h1>
        <p className="text-gray-600">
          Build a professional onboarding portal for your client in minutes
        </p>
      </div>

      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <span className="text-sm font-medium text-gray-700">
            Step {currentStep + 1} of {steps.length}
          </span>
          <span className="text-sm text-gray-500">{Math.round(progress)}% complete</span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      {/* Steps Navigation */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className={`flex-1 ${index < steps.length - 1 ? 'mr-4' : ''}`}
            >
              <button
                onClick={() => handleStepClick(index)}
                className={`w-full text-left p-4 rounded-lg border transition-colors ${
                  index === currentStep
                    ? 'border-blue-500 bg-blue-50'
                    : index < currentStep || step.completed
                    ? 'border-green-500 bg-green-50'
                    : 'border-gray-200 bg-white hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div
                    className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                      index === currentStep
                        ? 'bg-blue-500 text-white'
                        : step.completed
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-200 text-gray-600'
                    }`}
                  >
                    {step.completed ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <span className="text-sm font-medium">{index + 1}</span>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-sm font-medium text-gray-900">{step.title}</h3>
                    <p className="text-xs text-gray-500 mt-1">{step.description}</p>
                  </div>
                </div>
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            {steps[currentStep].title}
            <Badge variant="outline">
              Step {currentStep + 1}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {renderStepContent()}
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStep === 0}
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>

        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => setShowPreview(true)}
            disabled={!wizardData.template}
          >
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </Button>

          <Button
            variant="outline"
            onClick={() => handleSave(true)}
            disabled={isLoading || !wizardData.template}
          >
            <Save className="h-4 w-4 mr-2" />
            Save Draft
          </Button>

          {currentStep === steps.length - 1 ? (
            <Button
              onClick={() => handleSave(false)}
              disabled={isLoading || !steps.every(step => step.completed)}
            >
              <Send className="h-4 w-4 mr-2" />
              Share with Client
            </Button>
          ) : (
            <Button onClick={handleNext} disabled={!steps[currentStep].completed}>
              Next
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          )}
        </div>
      </div>

      {/* Preview Modal */}
      {showPreview && (
        <PreviewModal
          isOpen={showPreview}
          onClose={() => setShowPreview(false)}
          portalData={wizardData}
        />
      )}
    </div>
  )
}