// components/portal/branding-editor.tsx
'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Upload, Palette, Eye, X } from 'lucide-react'

interface BrandingEditorProps {
  branding: {
    logoUrl?: string
    primaryColor: string
    secondaryColor: string
    customDomain?: string
  }
  onChange: (branding: any) => void
}

const presetColors = [
  { name: 'Blue', value: '#2563eb' },
  { name: 'Green', value: '#059669' },
  { name: 'Purple', value: '#7c3aed' },
  { name: 'Red', value: '#dc2626' },
  { name: 'Orange', value: '#ea580c' },
  { name: 'Pink', value: '#db2777' },
  { name: 'Indigo', value: '#4f46e5' },
  { name: 'Teal', value: '#0d9488' }
]

export function BrandingEditor({ branding, onChange }: BrandingEditorProps) {
  const [isUploadingLogo, setIsUploadingLogo] = useState(false)

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsUploadingLogo(true)
    try {
      // Create form data for file upload
      const formData = new FormData()
      formData.append('file', file)
      formData.append('folder', 'logos')

      const response = await fetch('/api/files/upload', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) throw new Error('Upload failed')

      const result = await response.json()
      onChange({ ...branding, logoUrl: result.url })
    } catch (error) {
      console.error('Logo upload error:', error)
      // Handle error (show toast, etc.)
    } finally {
      setIsUploadingLogo(false)
    }
  }

  const handleColorChange = (type: 'primaryColor' | 'secondaryColor', color: string) => {
    onChange({ ...branding, [type]: color })
  }

  const removeLogo = () => {
    onChange({ ...branding, logoUrl: '' })
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-2">Brand Customization</h3>
        <p className="text-sm text-gray-600">
          Customize your portal&apos;s appearance to match your brand
        </p>
      </div>

      {/* Logo Upload */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Upload className="h-5 w-5" />
            <span>Logo</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {branding.logoUrl ? (
            <div className="flex items-center space-x-4">
              <div className="w-20 h-20 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                <img
                  src={branding.logoUrl}
                  alt="Logo"
                  className="max-w-full max-h-full object-contain"
                />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium">Logo uploaded</p>
                <p className="text-xs text-gray-500">
                  Your logo will appear at the top of your client portal
                </p>
              </div>
              <Button variant="outline" size="sm" onClick={removeLogo}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
              <div className="text-center">
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-4">
                  <Label htmlFor="logo-upload" className="cursor-pointer">
                    <span className="mt-2 block text-sm font-medium text-gray-900">
                      Upload your logo
                    </span>
                    <span className="mt-1 block text-xs text-gray-500">
                      PNG, JPG up to 2MB
                    </span>
                  </Label>
                  <Input
                    id="logo-upload"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleLogoUpload}
                    disabled={isUploadingLogo}
                  />
                  <Button
                    variant="outline"
                    className="mt-2"
                    disabled={isUploadingLogo}
                    onClick={() => document.getElementById('logo-upload')?.click()}
                  >
                    {isUploadingLogo ? 'Uploading...' : 'Choose File'}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Color Scheme */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Palette className="h-5 w-5" />
            <span>Color Scheme</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Primary Color */}
          <div className="space-y-3">
            <Label>Primary Color</Label>
            <div className="flex items-center space-x-4">
              <div
                className="w-12 h-12 rounded-lg border-2 border-gray-200 cursor-pointer"
                style={{ backgroundColor: branding.primaryColor }}
                onClick={() => document.getElementById('primary-color')?.click()}
              />
              <Input
                id="primary-color"
                type="color"
                value={branding.primaryColor}
                onChange={(e) => handleColorChange('primaryColor', e.target.value)}
                className="w-16 h-12 p-1 border rounded-lg cursor-pointer"
              />
              <Input
                value={branding.primaryColor}
                onChange={(e) => handleColorChange('primaryColor', e.target.value)}
                placeholder="#2563eb"
                className="flex-1"
              />
            </div>
            <div className="grid grid-cols-4 gap-2">
              {presetColors.map((color) => (
                <button
                  key={color.value}
                  className="w-full h-8 rounded border-2 border-gray-200 hover:border-gray-400 transition-colors"
                  style={{ backgroundColor: color.value }}
                  onClick={() => handleColorChange('primaryColor', color.value)}
                  title={color.name}
                />
              ))}
            </div>
          </div>

          {/* Secondary Color */}
          <div className="space-y-3">
            <Label>Secondary Color</Label>
            <div className="flex items-center space-x-4">
              <div
                className="w-12 h-12 rounded-lg border-2 border-gray-200 cursor-pointer"
                style={{ backgroundColor: branding.secondaryColor }}
                onClick={() => document.getElementById('secondary-color')?.click()}
              />
              <Input
                id="secondary-color"
                type="color"
                value={branding.secondaryColor}
                onChange={(e) => handleColorChange('secondaryColor', e.target.value)}
                className="w-16 h-12 p-1 border rounded-lg cursor-pointer"
              />
              <Input
                value={branding.secondaryColor}
                onChange={(e) => handleColorChange('secondaryColor', e.target.value)}
                placeholder="#64748b"
                className="flex-1"
              />
            </div>
          </div>

          {/* Preview */}
          <div className="border rounded-lg p-4 bg-gray-50">
            <p className="text-sm font-medium mb-3">Preview</p>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div
                  className="w-4 h-4 rounded"
                  style={{ backgroundColor: branding.primaryColor }}
                />
                <span className="text-sm">Primary color (buttons, links)</span>
              </div>
              <div className="flex items-center space-x-2">
                <div
                  className="w-4 h-4 rounded"
                  style={{ backgroundColor: branding.secondaryColor }}
                />
                <span className="text-sm">Secondary color (text, borders)</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Custom Domain */}
      <Card>
        <CardHeader>
          <CardTitle>Custom Domain</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="custom-domain">Custom Subdomain</Label>
            <div className="flex">
              <Input
                id="custom-domain"
                value={branding.customDomain || ''}
                onChange={(e) => onChange({ ...branding, customDomain: e.target.value })}
                placeholder="yourcompany"
                className="rounded-r-none"
              />
              <div className="px-3 py-2 bg-gray-100 border border-l-0 rounded-r-md text-sm text-gray-600">
                .onboarding.app
              </div>
            </div>
            <p className="text-xs text-gray-500">
              Your clients will access their portal at: {branding.customDomain || 'yourcompany'}.onboarding.app
            </p>
          </div>
          <Badge variant="outline" className="text-xs">
            Pro Feature
          </Badge>
        </CardContent>
      </Card>
    </div>
  )
}