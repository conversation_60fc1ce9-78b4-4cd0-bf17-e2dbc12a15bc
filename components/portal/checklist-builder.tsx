// components/portal/checklist-builder.tsx
'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
} from '@hello-pangea/dnd'
import {
  Plus,
  Trash2,
  GripVertical,
  CheckSquare,
  User,
  Users,
  UserCheck
} from 'lucide-react'
import { ChecklistItem, ASSIGNEE_TYPES } from '@/lib/types'

interface ChecklistBuilderProps {
  items: ChecklistItem[]
  onChange: (items: ChecklistItem[]) => void
}

const assigneeTypeIcons = {
  FREELANCER: User,
  CLIENT: User<PERSON>he<PERSON>,
  BOTH: Users,
}

const assigneeTypeLabels = {
  FREELANCER: 'Freelancer',
  CLIENT: 'Client',
  BOTH: 'Both',
}

const assigneeTypeColors = {
  FREELANCER: 'bg-blue-100 text-blue-800',
  CLIENT: 'bg-green-100 text-green-800',
  BOTH: 'bg-purple-100 text-purple-800',
}

export function ChecklistBuilder({ items, onChange }: ChecklistBuilderProps) {
  const [editingItem, setEditingItem] = useState<string | null>(null)

  const addItem = () => {
    const newItem: ChecklistItem = {
      id: `item_${Date.now()}`,
      title: 'New Task',
      description: '',
      completed: false,
      assigneeType: 'CLIENT',
      order: items.length
    }
    onChange([...items, newItem])
    setEditingItem(newItem.id)
  }

  const updateItem = (itemId: string, updates: Partial<ChecklistItem>) => {
    const updatedItems = items.map(item =>
      item.id === itemId ? { ...item, ...updates } : item
    )
    onChange(updatedItems)
  }

  const deleteItem = (itemId: string) => {
    const updatedItems = items.filter(item => item.id !== itemId)
    onChange(updatedItems)
  }

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return

    const reorderedItems = Array.from(items)
    const [movedItem] = reorderedItems.splice(result.source.index, 1)
    reorderedItems.splice(result.destination.index, 0, movedItem)

    // Update order values
    const updatedItems = reorderedItems.map((item, index) => ({
      ...item,
      order: index
    }))

    onChange(updatedItems)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Checklist Items</h3>
          <p className="text-sm text-gray-600">
            Define the steps and tasks for your onboarding process
          </p>
        </div>
        <Button onClick={addItem}>
          <Plus className="h-4 w-4 mr-2" />
          Add Task
        </Button>
      </div>

      {items.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <CheckSquare className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No tasks yet</h3>
            <p className="text-gray-600 text-center mb-4">
              Add your first checklist item to organize your onboarding process
            </p>
            <Button onClick={addItem}>
              <Plus className="h-4 w-4 mr-2" />
              Add Task
            </Button>
          </CardContent>
        </Card>
      ) : (
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="checklist-items">
            {(provided) => (
              <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-4">
                {items
                  .sort((a, b) => a.order - b.order)
                  .map((item, index) => {
                    const IconComponent = assigneeTypeIcons[item.assigneeType as keyof typeof assigneeTypeIcons] || User
                    const isEditing = editingItem === item.id

                    return (
                      <Draggable key={item.id} draggableId={item.id} index={index}>
                        {(provided, snapshot) => (
                          <Card
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            className={`${snapshot.isDragging ? 'shadow-lg' : ''}`}
                          >
                            <CardHeader className="pb-3">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                  <div
                                    {...provided.dragHandleProps}
                                    className="p-1 text-gray-400 hover:text-gray-600 cursor-grab"
                                  >
                                    <GripVertical className="h-4 w-4" />
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <span className="text-sm font-medium text-gray-500">
                                      {index + 1}.
                                    </span>
                                    <CheckSquare className="h-4 w-4 text-gray-400" />
                                  </div>
                                  <div className="flex-1">
                                    <h4 className="font-medium">{item.title}</h4>
                                    {item.description && (
                                      <p className="text-sm text-gray-600 mt-1">{item.description}</p>
                                    )}
                                    <div className="flex items-center space-x-2 mt-2">
                                      <Badge className={assigneeTypeColors[item.assigneeType as keyof typeof assigneeTypeColors]}>
                                        <IconComponent className="h-3 w-3 mr-1" />
                                        {assigneeTypeLabels[item.assigneeType as keyof typeof assigneeTypeLabels]}
                                      </Badge>
                                    </div>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setEditingItem(isEditing ? null : item.id)}
                                  >
                                    {isEditing ? 'Done' : 'Edit'}
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => deleteItem(item.id)}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            </CardHeader>
                            {isEditing && (
                              <CardContent className="space-y-4">
                                <div className="space-y-2">
                                  <Label>Task Title</Label>
                                  <Input
                                    value={item.title}
                                    onChange={(e) => updateItem(item.id, { title: e.target.value })}
                                    placeholder="Enter task title"
                                  />
                                </div>

                                <div className="space-y-2">
                                  <Label>Description (Optional)</Label>
                                  <Textarea
                                    value={item.description}
                                    onChange={(e) => updateItem(item.id, { description: e.target.value })}
                                    placeholder="Provide additional details about this task"
                                    rows={2}
                                  />
                                </div>

                                <div className="space-y-2">
                                  <Label>Assigned To</Label>
                                  <Select
                                    value={item.assigneeType}
                                    onValueChange={(value) => updateItem(item.id, { assigneeType: value as any })}
                                  >
                                    <SelectTrigger>
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {Object.entries(assigneeTypeLabels).map(([value, label]) => (
                                        <SelectItem key={value} value={value}>
                                          <div className="flex items-center space-x-2">
                                            {React.createElement(assigneeTypeIcons[value as keyof typeof assigneeTypeIcons], {
                                              className: "h-4 w-4"
                                            })}
                                            <span>{label}</span>
                                          </div>
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                  <p className="text-xs text-gray-500">
                                    {item.assigneeType === 'FREELANCER' && 'Only you can mark this task as complete'}
                                    {item.assigneeType === 'CLIENT' && 'Only the client can mark this task as complete'}
                                    {item.assigneeType === 'BOTH' && 'Either you or the client can mark this task as complete'}
                                  </p>
                                </div>
                              </CardContent>
                            )}
                          </Card>
                        )}
                      </Draggable>
                    )
                  })}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      )}
    </div>
  )
}