// components/portal/form-builder.tsx
'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
} from '@hello-pangea/dnd'
import {
  Plus,
  Trash2,
  GripVertical,
  Type,
  Mail,
  Phone,
  Calendar,
  FileText,
  List,
  Upload,
  AlignLeft
} from 'lucide-react'
import { FormField, FIELD_TYPES } from '@/lib/types'

interface FormBuilderProps {
  fields: FormField[]
  onChange: (fields: FormField[]) => void
}

const fieldTypeIcons = {
  TEXT: Type,
  EMAIL: Mail,
  PHONE: Phone,
  DATE: Calendar,
  TEXTAREA: AlignLeft,
  SELECT: List,
  MULTISELECT: List,
  FILE_UPLOAD: Upload,
}

const fieldTypeLabels = {
  TEXT: 'Text Input',
  EMAIL: 'Email',
  PHONE: 'Phone',
  DATE: 'Date',
  TEXTAREA: 'Long Text',
  SELECT: 'Dropdown',
  MULTISELECT: 'Multi-Select',
  FILE_UPLOAD: 'File Upload',
}

export function FormBuilder({ fields, onChange }: FormBuilderProps) {
  const [editingField, setEditingField] = useState<string | null>(null)

  const addField = () => {
    const newField: FormField = {
      id: `field_${Date.now()}`,
      label: 'New Field',
      type: 'TEXT',
      required: false,
      order: fields.length,
      options: []
    }
    onChange([...fields, newField])
    setEditingField(newField.id)
  }

  const updateField = (fieldId: string, updates: Partial<FormField>) => {
    const updatedFields = fields.map(field =>
      field.id === fieldId ? { ...field, ...updates } : field
    )
    onChange(updatedFields)
  }

  const deleteField = (fieldId: string) => {
    const updatedFields = fields.filter(field => field.id !== fieldId)
    onChange(updatedFields)
  }

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return

    const reorderedFields = Array.from(fields)
    const [movedField] = reorderedFields.splice(result.source.index, 1)
    reorderedFields.splice(result.destination.index, 0, movedField)

    // Update order values
    const updatedFields = reorderedFields.map((field, index) => ({
      ...field,
      order: index
    }))

    onChange(updatedFields)
  }

  const addOption = (fieldId: string) => {
    const field = fields.find(f => f.id === fieldId)
    if (field) {
      const newOptions = [...(field.options || []), `Option ${(field.options?.length || 0) + 1}`]
      updateField(fieldId, { options: newOptions })
    }
  }

  const updateOption = (fieldId: string, optionIndex: number, value: string) => {
    const field = fields.find(f => f.id === fieldId)
    if (field && field.options) {
      const newOptions = [...field.options]
      newOptions[optionIndex] = value
      updateField(fieldId, { options: newOptions })
    }
  }

  const removeOption = (fieldId: string, optionIndex: number) => {
    const field = fields.find(f => f.id === fieldId)
    if (field && field.options) {
      const newOptions = field.options.filter((_, index) => index !== optionIndex)
      updateField(fieldId, { options: newOptions })
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Form Fields</h3>
          <p className="text-sm text-gray-600">
            Customize the information you need to collect from your clients
          </p>
        </div>
        <Button onClick={addField}>
          <Plus className="h-4 w-4 mr-2" />
          Add Field
        </Button>
      </div>

      {fields.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No fields yet</h3>
            <p className="text-gray-600 text-center mb-4">
              Add your first form field to start collecting client information
            </p>
            <Button onClick={addField}>
              <Plus className="h-4 w-4 mr-2" />
              Add Field
            </Button>
          </CardContent>
        </Card>
      ) : (
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="form-fields">
            {(provided) => (
              <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-4">
                {fields
                  .sort((a, b) => a.order - b.order)
                  .map((field, index) => {
                    const IconComponent = fieldTypeIcons[field.type as keyof typeof fieldTypeIcons] || Type
                    const isEditing = editingField === field.id

                    return (
                      <Draggable key={field.id} draggableId={field.id} index={index}>
                        {(provided, snapshot) => (
                          <Card
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            className={`${snapshot.isDragging ? 'shadow-lg' : ''}`}
                          >
                            <CardHeader className="pb-3">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                  <div
                                    {...provided.dragHandleProps}
                                    className="p-1 text-gray-400 hover:text-gray-600 cursor-grab"
                                  >
                                    <GripVertical className="h-4 w-4" />
                                  </div>
                                  <div className="p-2 bg-gray-100 rounded-lg">
                                    <IconComponent className="h-4 w-4 text-gray-600" />
                                  </div>
                                  <div>
                                    <h4 className="font-medium">{field.label}</h4>
                                    <div className="flex items-center space-x-2">
                                      <Badge variant="outline">
                                        {fieldTypeLabels[field.type as keyof typeof fieldTypeLabels]}
                                      </Badge>
                                      {field.required && (
                                        <Badge variant="secondary">Required</Badge>
                                      )}
                                    </div>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setEditingField(isEditing ? null : field.id)}
                                  >
                                    {isEditing ? 'Done' : 'Edit'}
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => deleteField(field.id)}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            </CardHeader>
                            {isEditing && (
                              <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div className="space-y-2">
                                    <Label>Field Label</Label>
                                    <Input
                                      value={field.label}
                                      onChange={(e) => updateField(field.id, { label: e.target.value })}
                                      placeholder="Enter field label"
                                    />
                                  </div>
                                  <div className="space-y-2">
                                    <Label>Field Type</Label>
                                    <Select
                                      value={field.type}
                                      onValueChange={(value) => updateField(field.id, { type: value as any })}
                                    >
                                      <SelectTrigger>
                                        <SelectValue />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {Object.entries(fieldTypeLabels).map(([value, label]) => (
                                          <SelectItem key={value} value={value}>
                                            {label}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  </div>
                                </div>

                                <div className="flex items-center space-x-2">
                                  <Switch
                                    checked={field.required}
                                    onCheckedChange={(checked) => updateField(field.id, { required: checked })}
                                  />
                                  <Label>Required field</Label>
                                </div>

                                {(field.type === 'SELECT' || field.type === 'MULTISELECT') && (
                                  <div className="space-y-2">
                                    <Label>Options</Label>
                                    <div className="space-y-2">
                                      {field.options?.map((option, optionIndex) => (
                                        <div key={optionIndex} className="flex items-center space-x-2">
                                          <Input
                                            value={option}
                                            onChange={(e) => updateOption(field.id, optionIndex, e.target.value)}
                                            placeholder={`Option ${optionIndex + 1}`}
                                          />
                                          <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => removeOption(field.id, optionIndex)}
                                          >
                                            <Trash2 className="h-3 w-3" />
                                          </Button>
                                        </div>
                                      ))}
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => addOption(field.id)}
                                      >
                                        <Plus className="h-3 w-3 mr-2" />
                                        Add Option
                                      </Button>
                                    </div>
                                  </div>
                                )}
                              </CardContent>
                            )}
                          </Card>
                        )}
                      </Draggable>
                    )
                  })}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      )}
    </div>
  )
}