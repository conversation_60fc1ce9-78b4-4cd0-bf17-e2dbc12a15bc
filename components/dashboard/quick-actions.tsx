// components/dashboard/quick-actions.tsx
'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Plus, FileText, Users, Settings } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface QuickActionsProps {
  recentProjects?: any[]
  templates?: any[]
}

export function QuickActions({ recentProjects = [], templates = [] }: QuickActionsProps) {
  const router = useRouter()

  const actions = [
    {
      title: 'Create New Project',
      description: 'Start a new client onboarding project',
      icon: Plus,
      action: () => router.push('/projects/new'),
      color: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      title: 'Browse Templates',
      description: 'Explore our library of templates',
      icon: FileText,
      action: () => router.push('/templates'),
      color: 'bg-green-600 hover:bg-green-700'
    },
    {
      title: 'Manage Clients',
      description: 'View and manage your clients',
      icon: Users,
      action: () => router.push('/clients'),
      color: 'bg-purple-600 hover:bg-purple-700'
    },
    {
      title: 'Settings',
      description: 'Configure your account settings',
      icon: Settings,
      action: () => router.push('/settings'),
      color: 'bg-gray-600 hover:bg-gray-700'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {actions.map((action) => (
        <Card key={action.title} className="hover:shadow-md transition-shadow cursor-pointer" onClick={action.action}>
          <CardHeader className="pb-3">
            <div className={`w-10 h-10 rounded-lg ${action.color} flex items-center justify-center mb-2`}>
              <action.icon className="h-5 w-5 text-white" />
            </div>
            <CardTitle className="text-lg">{action.title}</CardTitle>
            <CardDescription className="text-sm">
              {action.description}
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <Button variant="outline" size="sm" className="w-full">
              Get Started
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
