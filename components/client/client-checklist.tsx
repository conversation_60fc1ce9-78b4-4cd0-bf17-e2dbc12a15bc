// components/client/client-checklist.tsx
'use client'

import { useState } from 'react'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { CheckCircle, Clock, User, Users, UserCheck } from 'lucide-react'

interface ChecklistItem {
  id: string
  title: string
  description?: string
  assigneeType: 'FREELANCER' | 'CLIENT' | 'BOTH'
  order: number
}

interface ClientChecklistProps {
  items: ChecklistItem[]
  completedTasks: string[]
  onTaskComplete: (taskIds: string[]) => void
  disabled?: boolean
  primaryColor?: string
}

const assigneeTypeIcons = {
  FREELANCER: User,
  CLIENT: UserCheck,
  BOTH: Users,
}

const assigneeTypeLabels = {
  FREELANCER: 'Provider Task',
  CLIENT: 'Your Task',
  BOTH: 'Shared Task',
}

const assigneeTypeColors = {
  FREELANCER: 'bg-blue-100 text-blue-800',
  CLIENT: 'bg-green-100 text-green-800',
  BOTH: 'bg-purple-100 text-purple-800',
}

export function ClientChecklist({ 
  items, 
  completedTasks, 
  onTaskComplete, 
  disabled = false,
  primaryColor = '#2563eb'
}: ClientChecklistProps) {
  const handleTaskToggle = (taskId: string, checked: boolean) => {
    if (disabled) return

    const updatedTasks = checked
      ? [...completedTasks, taskId]
      : completedTasks.filter(id => id !== taskId)
    
    onTaskComplete(updatedTasks)
  }

  const sortedItems = items.sort((a, b) => a.order - b.order)
  const completedCount = completedTasks.length
  const totalCount = items.length
  const progress = totalCount > 0 ? (completedCount / totalCount) * 100 : 0

  return (
    <div className="space-y-6">
      {/* Progress Summary */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Checklist Progress</span>
          <span className="text-sm text-gray-500">
            {completedCount} of {totalCount} completed
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="h-2 rounded-full transition-all duration-300"
            style={{ 
              width: `${progress}%`,
              backgroundColor: primaryColor
            }}
          />
        </div>
      </div>

      {/* Checklist Items */}
      <div className="space-y-4">
        {sortedItems.map((item, index) => {
          const isCompleted = completedTasks.includes(item.id)
          const canComplete = !disabled && (item.assigneeType === 'CLIENT' || item.assigneeType === 'BOTH')
          const IconComponent = assigneeTypeIcons[item.assigneeType]

          return (
            <div
              key={item.id}
              className={`border rounded-lg p-4 transition-all ${
                isCompleted ? 'bg-green-50 border-green-200' : 'bg-white border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-start space-x-4">
                <div className="flex items-center mt-1">
                  <span className="text-sm font-medium text-gray-500 mr-3">
                    {index + 1}.
                  </span>
                  {canComplete ? (
                    <Checkbox
                      checked={isCompleted}
                      onCheckedChange={(checked) => handleTaskToggle(item.id, checked as boolean)}
                      disabled={disabled}
                      className="data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                    />
                  ) : (
                    <div className="w-4 h-4 border rounded border-gray-300 flex items-center justify-center">
                      {isCompleted && <CheckCircle className="w-3 h-3 text-green-500" />}
                    </div>
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 pr-4">
                      <h4 className={`font-medium ${isCompleted ? 'text-green-900 line-through' : 'text-gray-900'}`}>
                        {item.title}
                      </h4>
                      {item.description && (
                        <p className={`text-sm mt-1 ${isCompleted ? 'text-green-700' : 'text-gray-600'}`}>
                          {item.description}
                        </p>
                      )}
                    </div>

                    <div className="flex items-center space-x-2">
                      <Badge 
                        variant="outline" 
                        className={`${assigneeTypeColors[item.assigneeType]} flex items-center gap-1`}
                      >
                        <IconComponent className="h-3 w-3" />
                        {assigneeTypeLabels[item.assigneeType]}
                      </Badge>
                      
                      {isCompleted && (
                        <Badge variant="default" className="bg-green-500">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Done
                        </Badge>
                      )}
                    </div>
                  </div>

                  {!canComplete && !isCompleted && (
                    <div className="mt-2">
                      <Badge variant="outline" className="text-xs bg-yellow-50 text-yellow-800 border-yellow-200">
                        <Clock className="h-3 w-3 mr-1" />
                        Waiting for provider
                      </Badge>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Summary */}
      {totalCount > 0 && (
        <div className="text-center pt-4 border-t">
          {completedCount === totalCount ? (
            <div className="text-green-600">
              <CheckCircle className="h-6 w-6 mx-auto mb-2" />
              <p className="font-medium">All tasks completed!</p>
            </div>
          ) : (
            <p className="text-gray-600">
              {completedCount} of {totalCount} tasks completed
            </p>
          )}
        </div>
      )}
    </div>
  )
}