// components/client/file-upload.tsx
'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Upload, 
  File, 
  Image, 
  FileText, 
  Download, 
  Trash2, 
  Plus,
  X,
  CheckCircle
} from 'lucide-react'

interface FileItem {
  id?: string
  name: string
  size: number
  type: string
  url: string
  uploadedAt?: string
}

interface FileUploadProps {
  files: FileItem[]
  onFilesChange: (files: FileItem[]) => void
  token: string
  disabled?: boolean
  maxSize?: number // in MB
  allowedTypes?: string[]
}

export function FileUpload({ 
  files, 
  onFilesChange, 
  token, 
  disabled = false,
  maxSize = 10,
  allowedTypes = ['image/*', 'application/pdf', '.doc', '.docx', '.txt']
}: FileUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [dragOver, setDragOver] = useState(false)

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return Image
    if (type.includes('pdf')) return FileText
    return File
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const validateFile = (file: File) => {
    // Check file size
    if (file.size > maxSize * 1024 * 1024) {
      throw new Error(`File ${file.name} is too large. Maximum size is ${maxSize}MB.`)
    }

    // Check file type
    const isAllowed = allowedTypes.some(type => {
      if (type.includes('*')) {
        return file.type.startsWith(type.replace('*', ''))
      }
      return file.type === type || file.name.toLowerCase().endsWith(type)
    })

    if (!isAllowed) {
      throw new Error(`File type not allowed: ${file.name}`)
    }
  }

  const uploadFiles = async (fileList: FileList) => {
    if (disabled || fileList.length === 0) return

    setUploading(true)
    setUploadProgress(0)

    try {
      const uploadedFiles: FileItem[] = []
      const totalFiles = fileList.length

      for (let i = 0; i < fileList.length; i++) {
        const file = fileList[i]
        
        try {
          validateFile(file)

          const formData = new FormData()
          formData.append('file', file)
          formData.append('token', token)
          formData.append('folder', 'client-files')

          const response = await fetch('/api/files/upload', {
            method: 'POST',
            body: formData
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Upload failed')
          }

          const result = await response.json()
          uploadedFiles.push({
            id: result.id,
            name: file.name,
            size: file.size,
            type: file.type,
            url: result.url,
            uploadedAt: new Date().toISOString()
          })

          setUploadProgress(((i + 1) / totalFiles) * 100)
        } catch (error) {
          console.error(`Error uploading ${file.name}:`, error)
          // Continue with other files
        }
      }

      if (uploadedFiles.length > 0) {
        onFilesChange([...files, ...uploadedFiles])
      }
    } catch (error) {
      console.error('Upload error:', error)
    } finally {
      setUploading(false)
      setUploadProgress(0)
    }
  }

  const removeFile = (index: number) => {
    if (disabled) return
    const updatedFiles = files.filter((_, i) => i !== index)
    onFilesChange(updatedFiles)
  }

  const downloadFile = (file: FileItem) => {
    const link = document.createElement('a')
    link.href = file.url
    link.download = file.name
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    if (e.dataTransfer.files) {
      uploadFiles(e.dataTransfer.files)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      {!disabled && (
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            dragOver 
              ? 'border-blue-400 bg-blue-50' 
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          <Upload className={`mx-auto h-12 w-12 mb-4 ${dragOver ? 'text-blue-500' : 'text-gray-400'}`} />
          <div className="space-y-2">
            <h3 className="text-lg font-medium text-gray-900">
              {dragOver ? 'Drop files here' : 'Upload files'}
            </h3>
            <p className="text-sm text-gray-600">
              Drag and drop files here, or click to browse
            </p>
            <p className="text-xs text-gray-500">
              Max file size: {maxSize}MB. Supported types: Images, PDF, Documents
            </p>
          </div>
          
          <div className="mt-4">
            <input
              type="file"
              multiple
              onChange={(e) => e.target.files && uploadFiles(e.target.files)}
              className="hidden"
              id="file-upload"
              accept={allowedTypes.join(',')}
              disabled={uploading}
            />
            <Button
              type="button"
              onClick={() => document.getElementById('file-upload')?.click()}
              disabled={uploading}
              variant="outline"
            >
              <Plus className="h-4 w-4 mr-2" />
              {uploading ? 'Uploading...' : 'Choose Files'}
            </Button>
          </div>

          {uploading && (
            <div className="mt-4 space-y-2">
              <Progress value={uploadProgress} className="w-full" />
              <p className="text-sm text-gray-600">
                Uploading... {Math.round(uploadProgress)}%
              </p>
            </div>
          )}
        </div>
      )}

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900 flex items-center">
            <File className="h-4 w-4 mr-2" />
            Uploaded Files ({files.length})
          </h4>
          
          <div className="grid gap-3">
            {files.map((file, index) => {
              const IconComponent = getFileIcon(file.type)
              
              return (
                <Card key={index} className="hover:shadow-sm transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 flex-1 min-w-0">
                        <div className="p-2 bg-gray-100 rounded-lg">
                          <IconComponent className="h-5 w-5 text-gray-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {file.name}
                          </p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {formatFileSize(file.size)}
                            </Badge>
                            {file.uploadedAt && (
                              <span className="text-xs text-gray-500">
                                {new Date(file.uploadedAt).toLocaleDateString()}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => downloadFile(file)}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        {!disabled && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeFile(index)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      )}

      {/* Empty State */}
      {files.length === 0 && disabled && (
        <div className="text-center py-8 text-gray-500">
          <File className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p>No files uploaded yet</p>
        </div>
      )}
    </div>
  )
}