// components/client/client-form.tsx
'use client'

import { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Upload, X, File } from 'lucide-react'

interface FormField {
  id: string
  label: string
  type: string
  required: boolean
  options?: string[]
  value?: string
}

interface ClientFormProps {
  fields: FormField[]
  data: Record<string, any>
  onChange: (data: Record<string, any>) => void
  disabled?: boolean
}

export function ClientForm({ fields, data, onChange, disabled = false }: ClientFormProps) {
  const [uploadingFiles, setUploadingFiles] = useState<Record<string, boolean>>({})

  const handleFieldChange = (fieldId: string, value: any) => {
    if (disabled) return
    onChange({ ...data, [fieldId]: value })
  }

  const handleFileUpload = async (fieldId: string, files: FileList) => {
    if (disabled || files.length === 0) return

    setUploadingFiles(prev => ({ ...prev, [fieldId]: true }))
    
    try {
      const uploadedFiles = []
      
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        const formData = new FormData()
        formData.append('file', file)
        formData.append('folder', 'client-uploads')

        const response = await fetch('/api/files/upload', {
          method: 'POST',
          body: formData
        })

        if (response.ok) {
          const result = await response.json()
          uploadedFiles.push({
            name: file.name,
            url: result.url,
            size: file.size
          })
        }
      }

      const existingFiles = data[fieldId] || []
      handleFieldChange(fieldId, [...existingFiles, ...uploadedFiles])
    } catch (error) {
      console.error('File upload error:', error)
    } finally {
      setUploadingFiles(prev => ({ ...prev, [fieldId]: false }))
    }
  }

  const removeFile = (fieldId: string, fileIndex: number) => {
    if (disabled) return
    const files = data[fieldId] || []
    const updatedFiles = files.filter((_: any, index: number) => index !== fileIndex)
    handleFieldChange(fieldId, updatedFiles)
  }

  const renderField = (field: FormField) => {
    const value = data[field.id] || ''

    switch (field.type) {
      case 'TEXT':
      case 'EMAIL':
      case 'PHONE':
        return (
          <Input
            type={field.type === 'EMAIL' ? 'email' : field.type === 'PHONE' ? 'tel' : 'text'}
            value={value}
            onChange={(e) => handleFieldChange(field.id, e.target.value)}
            placeholder={`Enter ${field.label.toLowerCase()}`}
            disabled={disabled}
          />
        )

      case 'TEXTAREA':
        return (
          <Textarea
            value={value}
            onChange={(e) => handleFieldChange(field.id, e.target.value)}
            placeholder={`Enter ${field.label.toLowerCase()}`}
            rows={4}
            disabled={disabled}
          />
        )

      case 'DATE':
        return (
          <Input
            type="date"
            value={value}
            onChange={(e) => handleFieldChange(field.id, e.target.value)}
            disabled={disabled}
          />
        )

      case 'SELECT':
        return (
          <select
            value={value}
            onChange={(e) => handleFieldChange(field.id, e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={disabled}
          >
            <option value="">Select an option</option>
            {field.options?.map((option, index) => (
              <option key={index} value={option}>
                {option}
              </option>
            ))}
          </select>
        )

      case 'MULTISELECT':
        const selectedValues = Array.isArray(value) ? value : []
        return (
          <div className="space-y-2">
            {field.options?.map((option, index) => (
              <label key={index} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={selectedValues.includes(option)}
                  onChange={(e) => {
                    const newValues = e.target.checked
                      ? [...selectedValues, option]
                      : selectedValues.filter(v => v !== option)
                    handleFieldChange(field.id, newValues)
                  }}
                  disabled={disabled}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm">{option}</span>
              </label>
            ))}
          </div>
        )

      case 'FILE_UPLOAD':
        const files = data[field.id] || []
        const isUploading = uploadingFiles[field.id] || false

        return (
          <div className="space-y-3">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
              <Upload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
              <div className="text-sm text-gray-600 mb-2">
                Drop files here or click to browse
              </div>
              <input
                type="file"
                multiple
                onChange={(e) => e.target.files && handleFileUpload(field.id, e.target.files)}
                className="hidden"
                id={`file-${field.id}`}
                disabled={disabled || isUploading}
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => document.getElementById(`file-${field.id}`)?.click()}
                disabled={disabled || isUploading}
              >
                {isUploading ? 'Uploading...' : 'Choose Files'}
              </Button>
            </div>

            {files.length > 0 && (
              <div className="space-y-2">
                {files.map((file: any, index: number) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <div className="flex items-center space-x-2">
                      <File className="h-4 w-4 text-gray-500" />
                      <span className="text-sm truncate">{file.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {(file.size / 1024).toFixed(1)} KB
                      </Badge>
                    </div>
                    {!disabled && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(field.id, index)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )

      default:
        return (
          <Input
            value={value}
            onChange={(e) => handleFieldChange(field.id, e.target.value)}
            placeholder={`Enter ${field.label.toLowerCase()}`}
            disabled={disabled}
          />
        )
    }
  }

  return (
    <div className="space-y-6">
      {fields.map((field) => (
        <div key={field.id} className="space-y-2">
          <Label htmlFor={field.id} className="text-sm font-medium text-gray-700">
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
          {renderField(field)}
        </div>
      ))}
    </div>
  )
}