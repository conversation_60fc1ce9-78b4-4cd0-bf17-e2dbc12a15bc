// components/client/client-portal.tsx
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ClientForm } from './client-form'
import { ClientChecklist } from './client-checklist'
import { FileUpload } from './file-upload'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { CheckCircle, Clock, AlertTriangle, FileText, List, Upload } from 'lucide-react'

interface ClientPortalProps {
  token: string
}

export function ClientPortal({ token }: ClientPortalProps) {
  const [portalData, setPortalData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string>('')
  const [submitting, setSubmitting] = useState(false)
  const [formData, setFormData] = useState<any>({})
  const [completedTasks, setCompletedTasks] = useState<string[]>([])
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([])

  useEffect(() => {
    loadPortalData()
  }, [token])

  const loadPortalData = async () => {
    try {
      const response = await fetch(`/api/portal/${token}`)
      if (!response.ok) {
        throw new Error('Portal not found')
      }
      const data = await response.json()
      setPortalData(data)
      
      // Initialize form data with existing values
      if (data.clientData) {
        setFormData(data.clientData.formData || {})
        setCompletedTasks(data.clientData.completedTasks || [])
        setUploadedFiles(data.clientData.uploadedFiles || [])
      }
    } catch (err) {
      setError('Unable to load portal. Please check your link.')
    } finally {
      setLoading(false)
    }
  }

  const calculateProgress = () => {
    if (!portalData) return 0
    
    const totalItems = portalData.formFields.length + portalData.checklistItems.length
    const completedItems = Object.keys(formData).length + completedTasks.length
    
    return totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0
  }

  const handleSubmit = async () => {
    setSubmitting(true)
    try {
      const response = await fetch(`/api/portal/${token}/submit`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          formData,
          completedTasks,
          uploadedFiles,
          status: 'submitted'
        })
      })

      if (!response.ok) throw new Error('Submission failed')

      // Show success message and refresh data
      await loadPortalData()
    } catch (err) {
      setError('Failed to submit. Please try again.')
    } finally {
      setSubmitting(false)
    }
  }

  const handleSaveProgress = async () => {
    try {
      await fetch(`/api/portal/${token}/submit`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          formData,
          completedTasks,
          uploadedFiles,
          status: 'in_progress'
        })
      })
    } catch (err) {
      console.error('Failed to save progress:', err)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardContent className="text-center py-8">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Portal Not Found</h2>
            <p className="text-gray-600">{error}</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  const progress = calculateProgress()
  const isComplete = progress === 100

  return (
    <div 
      className="min-h-screen"
      style={{ backgroundColor: '#f9fafb' }}
    >
      {/* Header */}
      <div 
        className="bg-white shadow-sm"
        style={{ 
          borderTopColor: portalData.branding?.primaryColor || '#2563eb',
          borderTopWidth: '4px'
        }}
      >
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {portalData.branding?.logoUrl && (
                <img
                  src={portalData.branding.logoUrl}
                  alt="Logo"
                  className="h-12 w-auto"
                />
              )}
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Welcome, {portalData.clientName}!
                </h1>
                <p className="text-gray-600">
                  {portalData.name} - Onboarding Portal
                </p>
              </div>
            </div>
            <Badge 
              variant={isComplete ? "default" : "secondary"}
              className="text-sm"
            >
              {isComplete ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-1" />
                  Complete
                </>
              ) : (
                <>
                  <Clock className="h-4 w-4 mr-1" />
                  In Progress
                </>
              )}
            </Badge>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-white border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Overall Progress</span>
            <span className="text-sm text-gray-500">{progress}% complete</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 py-8 space-y-8">
        {portalData.status === 'completed' && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Thank you! Your onboarding has been completed and submitted successfully.
            </AlertDescription>
          </Alert>
        )}

        {/* Form Section */}
        {portalData.formFields && portalData.formFields.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>Project Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ClientForm
                fields={portalData.formFields}
                data={formData}
                onChange={setFormData}
                disabled={portalData.status === 'completed'}
              />
            </CardContent>
          </Card>
        )}

        {/* Checklist Section */}
        {portalData.checklistItems && portalData.checklistItems.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <List className="h-5 w-5" />
                <span>Onboarding Checklist</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ClientChecklist
                items={portalData.checklistItems}
                completedTasks={completedTasks}
                onTaskComplete={setCompletedTasks}
                disabled={portalData.status === 'completed'}
                primaryColor={portalData.branding?.primaryColor}
              />
            </CardContent>
          </Card>
        )}

        {/* File Upload Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Upload className="h-5 w-5" />
              <span>File Sharing</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <FileUpload
              files={uploadedFiles}
              onFilesChange={setUploadedFiles}
              token={token}
              disabled={portalData.status === 'completed'}
            />
          </CardContent>
        </Card>

        {/* Action Buttons */}
        {portalData.status !== 'completed' && (
          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              variant="outline"
              onClick={handleSaveProgress}
              className="flex-1"
              disabled={submitting}
            >
              Save Progress
            </Button>
            <Button
              onClick={handleSubmit}
              className="flex-1"
              disabled={submitting || !isComplete}
              style={{ 
                backgroundColor: isComplete ? portalData.branding?.primaryColor : undefined 
              }}
            >
              {submitting ? (
                <LoadingSpinner size="sm" />
              ) : (
                'Submit for Review'
              )}
            </Button>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="bg-white border-t mt-12">
        <div className="max-w-4xl mx-auto px-4 py-6 text-center">
          <p className="text-sm text-gray-500">
            Powered by Client Onboarding Assistant
          </p>
        </div>
      </div>
    </div>
  )
}