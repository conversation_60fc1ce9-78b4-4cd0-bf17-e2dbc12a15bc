// components/subscription/billing-portal.tsx
'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { Calendar, CreditCard, AlertTriangle } from 'lucide-react'

interface Subscription {
  id: string
  tier: string
  status: 'active' | 'canceled' | 'past_due'
  currentPeriodEnd: string
  cancelAtPeriodEnd: boolean
}

interface BillingPortalProps {
  subscription?: Subscription
  onManageBilling?: () => void
  onCancelSubscription?: () => void
  onReactivateSubscription?: () => void
}

export function BillingPortal({ 
  subscription, 
  onManageBilling,
  onCancelSubscription,
  onReactivateSubscription 
}: BillingPortalProps) {
  const [isLoading, setIsLoading] = useState(false)

  const handleManageBilling = async () => {
    setIsLoading(true)
    try {
      await onManageBilling?.()
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancelSubscription = async () => {
    setIsLoading(true)
    try {
      await onCancelSubscription?.()
    } finally {
      setIsLoading(false)
    }
  }

  const handleReactivateSubscription = async () => {
    setIsLoading(true)
    try {
      await onReactivateSubscription?.()
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>
      case 'canceled':
        return <Badge className="bg-red-100 text-red-800">Canceled</Badge>
      case 'past_due':
        return <Badge className="bg-yellow-100 text-yellow-800">Past Due</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  if (!subscription) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>No Active Subscription</CardTitle>
          <CardDescription>
            You don&apos;t have an active subscription. Choose a plan to get started.
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Current Subscription */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <CreditCard className="h-5 w-5" />
                <span>Current Subscription</span>
              </CardTitle>
              <CardDescription>
                Manage your billing and subscription settings
              </CardDescription>
            </div>
            {getStatusBadge(subscription.status)}
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-900">Plan</p>
              <p className="text-lg font-semibold capitalize">{subscription.tier}</p>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-900">Next billing date</p>
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <p className="text-sm">
                  {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <Button 
              onClick={handleManageBilling}
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading ? <LoadingSpinner size="sm" /> : 'Manage Billing'}
            </Button>
            
            {subscription.status === 'active' && !subscription.cancelAtPeriodEnd && (
              <Button 
                variant="outline"
                onClick={handleCancelSubscription}
                disabled={isLoading}
              >
                Cancel Subscription
              </Button>
            )}
            
            {subscription.cancelAtPeriodEnd && (
              <Button 
                variant="outline"
                onClick={handleReactivateSubscription}
                disabled={isLoading}
              >
                Reactivate
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
      
      {/* Cancellation Notice */}
      {subscription.cancelAtPeriodEnd && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Your subscription will be canceled at the end of the current billing period 
            ({new Date(subscription.currentPeriodEnd).toLocaleDateString()}). 
            Your subscription will remain active until the end of your current billing period and won&apos;t be renewed.
          </AlertDescription>
        </Alert>
      )}
      
      {/* Past Due Notice */}
      {subscription.status === 'past_due' && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Your payment is past due. Please update your payment method to continue using the service.
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
