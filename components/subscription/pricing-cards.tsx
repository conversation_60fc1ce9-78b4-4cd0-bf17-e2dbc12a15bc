// components/subscription/pricing-cards.tsx
'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { CheckCircle } from 'lucide-react'
import { SUBSCRIPTION_TIERS } from '@/lib/constants'

interface PricingCardsProps {
  currentTier?: string
  onSelectPlan?: (tier: string) => void
}

export function PricingCards({ currentTier, onSelectPlan }: PricingCardsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {SUBSCRIPTION_TIERS.map((tier) => (
        <Card 
          key={tier.id} 
          className={`relative ${
            tier.popular ? 'ring-2 ring-blue-600 scale-105' : ''
          } ${
            currentTier === tier.id ? 'border-green-500' : ''
          }`}
        >
          {tier.popular && (
            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <Badge className="bg-blue-600 text-white">Most Popular</Badge>
            </div>
          )}
          
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">{tier.name}</CardTitle>
            <div className="mt-4">
              <span className="text-4xl font-bold">{tier.price}</span>
              <span className="text-gray-600">/month</span>
            </div>
            <CardDescription>{tier.description}</CardDescription>
          </CardHeader>
          
          <CardContent>
            <ul className="space-y-3 mb-6">
              {tier.features.map((feature, index) => (
                <li key={index} className="flex items-start space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">{feature}</span>
                </li>
              ))}
            </ul>
            
            <Button 
              className="w-full" 
              variant={tier.popular ? 'default' : 'outline'}
              onClick={() => onSelectPlan?.(tier.id)}
              disabled={currentTier === tier.id}
            >
              {currentTier === tier.id ? 'Current Plan' : 'Select Plan'}
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
