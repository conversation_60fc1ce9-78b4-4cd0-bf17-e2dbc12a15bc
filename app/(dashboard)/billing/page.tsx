// app/(dashboard)/billing/page.tsx
'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { PricingCards } from '@/components/subscription/pricing-cards'
import { BillingPortal } from '@/components/subscription/billing-portal'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CreditCard, Crown, AlertTriangle } from 'lucide-react'

export default function BillingPage() {
  const { data: session } = useSession()
  const [subscription, setSubscription] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showPricing, setShowPricing] = useState(false)

  useEffect(() => {
    if (session) {
      loadSubscriptionData()
    }
  }, [session])

  const loadSubscriptionData = async () => {
    try {
      const response = await fetch('/api/billing/subscription')
      if (response.ok) {
        const data = await response.json()
        setSubscription(data.subscription)
      }
    } catch (err) {
      setError('Failed to load subscription data')
      console.error('Billing error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handlePlanSelect = async (planId: string) => {
    try {
      const response = await fetch('/api/billing/create-checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          priceId: getPriceId(planId),
          planType: planId 
        })
      })

      if (response.ok) {
        const data = await response.json()
        window.location.href = data.checkoutUrl
      } else {
        throw new Error('Failed to create checkout session')
      }
    } catch (err) {
      setError('Failed to start subscription process')
      console.error('Checkout error:', err)
    }
  }

  const handleManageBilling = async () => {
    try {
      const response = await fetch('/api/billing/portal', {
        method: 'POST'
      })

      if (response.ok) {
        const data = await response.json()
        window.location.href = data.url
      } else {
        throw new Error('Failed to create billing portal session')
      }
    } catch (err) {
      setError('Failed to open billing portal')
      console.error('Billing portal error:', err)
    }
  }

  const getPriceId = (planId: string) => {
    // These would be your actual Stripe price IDs
    const priceIds = {
      PRO: process.env.NEXT_PUBLIC_STRIPE_PRO_PRICE_ID || 'price_pro',
      BUSINESS: process.env.NEXT_PUBLIC_STRIPE_BUSINESS_PRICE_ID || 'price_business'
    }
    return priceIds[planId as keyof typeof priceIds]
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Billing & Subscription</h1>
          <p className="text-gray-600 mt-2">
            Manage your subscription and billing information
          </p>
        </div>
        {subscription?.tier !== 'FREE' && (
          <Button onClick={() => setShowPricing(!showPricing)} variant="outline">
            {showPricing ? 'Hide Plans' : 'View Plans'}
          </Button>
        )}
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Current Plan Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Crown className="h-5 w-5" />
            <span>Current Plan</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold">
                {subscription?.tier || 'FREE'} Plan
              </h3>
              <p className="text-gray-600">
                {subscription?.tier === 'PRO' ? '€10/month - Perfect for freelancers' :
                 subscription?.tier === 'BUSINESS' ? '€29/month - Great for agencies' :
                 'Free forever - Basic features'}
              </p>
            </div>
            <Badge 
              variant={subscription?.status === 'ACTIVE' ? 'default' : 'secondary'}
              className="text-lg px-4 py-2"
            >
              {subscription?.status || 'FREE'}
            </Badge>
          </div>
          
          {subscription?.tier === 'FREE' && (
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Ready to unlock more features?</h4>
              <p className="text-blue-700 text-sm mb-4">
                Upgrade to Pro or Business to get unlimited projects, advanced templates, and priority support.
              </p>
              <Button onClick={() => setShowPricing(true)}>
                View Plans & Pricing
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Billing Portal */}
      {subscription && subscription.tier !== 'FREE' && (
        <BillingPortal
          subscription={subscription}
          onManageBilling={handleManageBilling}
        />
      )}

      {/* Pricing Cards */}
      {(showPricing || !subscription || subscription.tier === 'FREE') && (
        <div>
          <h2 className="text-2xl font-bold text-center mb-8">Choose Your Plan</h2>
          <PricingCards
            currentTier={subscription?.tier || 'FREE'}
            onSelectPlan={handlePlanSelect}
          />
        </div>
      )}
    </div>
  )
}