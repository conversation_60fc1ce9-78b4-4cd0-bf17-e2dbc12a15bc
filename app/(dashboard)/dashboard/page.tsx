// app/(dashboard)/dashboard/page.tsx
'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { StatsCards } from '@/components/dashboard/stats-cards'
import { QuickActions } from '@/components/dashboard/quick-actions'
import { ProjectsList } from '@/components/dashboard/projects-list'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertTriangle, TrendingUp } from 'lucide-react'
import { useRouter } from 'next/navigation'

export default function DashboardPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [stats, setStats] = useState({
    totalProjects: 0,
    activeProjects: 0,
    completedProjects: 0,
    totalClients: 0,
    pendingResponses: 0,
    completionRate: 0,
    avgCompletionTime: 0
  })
  const [projects, setProjects] = useState([])
  const [templates, setTemplates] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    if (session) {
      loadDashboardData()
    }
  }, [session])

  const loadDashboardData = async () => {
    try {
      // Load projects
      const projectsResponse = await fetch('/api/projects?limit=5')
      if (projectsResponse.ok) {
        const projectsData = await projectsResponse.json()
        setProjects(projectsData.projects)
        
        // Calculate stats
        const total = projectsData.projects.length
        const active = projectsData.projects.filter((p: any) => p.status === 'IN_PROGRESS').length
        const completed = projectsData.projects.filter((p: any) => p.status === 'COMPLETED').length
        const pending = projectsData.projects.filter((p: any) => p.status === 'SHARED').length
        
        setStats({
          totalProjects: total,
          activeProjects: active,
          completedProjects: completed,
          totalClients: projectsData.projects.length, // Using project count as client count for now
          pendingResponses: pending,
          completionRate: total > 0 ? Math.round((completed / total) * 100) : 0,
          avgCompletionTime: 7 // Mock data
        })
      }

      // Load templates (mock data for now)
      setTemplates([
        { id: '1', name: 'Web Development', category: 'Development' },
        { id: '2', name: 'Brand Identity', category: 'Design' },
        { id: '3', name: 'Marketing Strategy', category: 'Marketing' }
      ])
    } catch (err) {
      setError('Failed to load dashboard data')
      console.error('Dashboard error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleProjectStatusChange = async (project: any) => {
    // This function signature matches the component expectation
    console.log('Status change for project:', project.id)
    loadDashboardData() // Reload dashboard data
  }

  const handleProjectDelete = async (project: any) => {
    if (!confirm('Are you sure you want to delete this project?')) return

    try {
      const response = await fetch(`/api/projects/${project.id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        loadDashboardData() // Reload dashboard data
      }
    } catch (err) {
      console.error('Delete error:', err)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome back, {session?.user?.name}!
        </h1>
        <p className="text-gray-600 mt-2">
          Here&apos;s what&apos;s happening with your client onboarding projects
        </p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      <StatsCards stats={stats} />

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Quick Actions</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <QuickActions 
            recentProjects={projects.slice(0, 3)} 
            templates={templates}
          />
        </CardContent>
      </Card>

      {/* Recent Projects */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Projects</CardTitle>
        </CardHeader>
        <CardContent>
          <ProjectsList
            projects={projects}
            onEdit={(project) => router.push(`/projects/${project.id}`)}
            onDelete={handleProjectDelete}
            onView={(project) => router.push(`/portal/${project.shareToken || project.id}`)}
          />
        </CardContent>
      </Card>
    </div>
  )
}