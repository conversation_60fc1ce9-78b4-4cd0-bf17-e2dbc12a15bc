// app/(dashboard)/projects/new/page.tsx
'use client'

import { PortalWizard } from '@/components/portal/portal-wizard'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'

export default function NewProjectPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/projects">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Projects
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Create New Project</h1>
          <p className="text-gray-600 mt-2">
            Build a professional onboarding portal for your client
          </p>
        </div>
      </div>

      {/* Portal Wizard */}
      <PortalWizard />
    </div>
  )
}