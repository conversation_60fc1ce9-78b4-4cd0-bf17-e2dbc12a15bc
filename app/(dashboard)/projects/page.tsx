// app/(dashboard)/projects/page.tsx
'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { ProjectsList } from '@/components/dashboard/projects-list'
import { Button } from '@/components/ui/button'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Plus, AlertTriangle } from 'lucide-react'

export default function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    loadProjects()
  }, [])

  const loadProjects = async () => {
    try {
      const response = await fetch('/api/projects')
      if (response.ok) {
        const data = await response.json()
        setProjects(data.projects)
      } else {
        throw new Error('Failed to load projects')
      }
    } catch (err) {
      setError('Failed to load projects')
      console.error('Projects error:', err)
    } finally {
      setLoading(false)
    }
  }

  interface Project {
    id: string
    name: string
    description: string
    status: 'draft' | 'active' | 'completed'
    clientCount: number
    createdAt: string
    updatedAt: string
    shareToken?: string
  }

  const handleProjectEdit = (project: Project) => {
    // Navigate to edit page
    window.location.href = `/projects/${project.id}`
  }

  const handleProjectDelete = async (project: Project) => {
    if (!confirm('Are you sure you want to delete this project?')) return

    try {
      const response = await fetch(`/api/projects/${project.id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        loadProjects() // Reload projects
      }
    } catch (err) {
      console.error('Delete error:', err)
    }
  }

  const handleProjectView = (project: Project) => {
    // Navigate to portal view
    window.location.href = `/portal/${project.shareToken || project.id}`
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Projects</h1>
          <p className="text-gray-600 mt-2">
            Manage your client onboarding projects
          </p>
        </div>
        <Link href="/projects/new">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Project
          </Button>
        </Link>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Projects List */}
      <ProjectsList
        projects={projects}
        onEdit={handleProjectEdit}
        onDelete={handleProjectDelete}
        onView={handleProjectView}
      />
    </div>
  )
}