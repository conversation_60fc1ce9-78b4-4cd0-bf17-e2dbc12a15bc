// app/api/projects/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { projectSchema } from '@/lib/validations'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const project = await prisma.project.findFirst({
      where: {
        id: params.id,
        userId: session.user.id
      },
      include: {
        formFields: { orderBy: { order: 'asc' } },
        checklistItems: { orderBy: { order: 'asc' } },
        clientData: true
      }
    })

    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    // Parse branding JSON
    const projectData = {
      ...project,
      branding: project.branding ? JSON.parse(project.branding as string) : null
    }

    return NextResponse.json({ project: projectData })
  } catch (error) {
    console.error('Get project error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const data = projectSchema.parse(body)

    // Verify ownership
    const existingProject = await prisma.project.findFirst({
      where: {
        id: params.id,
        userId: session.user.id
      }
    })

    if (!existingProject) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    // Update project using transaction
    const project = await prisma.$transaction(async (tx) => {
      // Delete existing form fields and checklist items
      await tx.formField.deleteMany({
        where: { projectId: params.id }
      })
      
      await tx.checklistItem.deleteMany({
        where: { projectId: params.id }
      })

      // Update project with new data
      return await tx.project.update({
        where: { id: params.id },
        data: {
          name: data.projectName,
          clientName: data.clientName,
          clientEmail: data.clientEmail,
          description: data.description,
          status: data.status || existingProject.status,
          branding: data.branding ? JSON.stringify(data.branding) : null,
          formFields: {
            create: data.formFields?.map((field, index) => ({
              ...field,
              order: index
            })) || []
          },
          checklistItems: {
            create: data.checklistItems?.map((item, index) => ({
              ...item,
              order: index
            })) || []
          }
        },
        include: {
          formFields: { orderBy: { order: 'asc' } },
          checklistItems: { orderBy: { order: 'asc' } }
        }
      })
    })

    return NextResponse.json({ project })
  } catch (error) {
    console.error('Update project error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify ownership
    const project = await prisma.project.findFirst({
      where: {
        id: params.id,
        userId: session.user.id
      }
    })

    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    // Delete project (cascade will handle related records)
    await prisma.project.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Project deleted successfully' })
  } catch (error) {
    console.error('Delete project error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}