// app/api/projects/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { projectSchema } from '@/lib/validations'
import { generateShareToken } from '@/lib/utils'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')

    const where: any = { userId: session.user.id }
    if (status && status !== 'all') {
      where.status = status
    }

    const projects = await prisma.project.findMany({
      where,
      include: {
        _count: {
          select: {
            formFields: true,
            checklistItems: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset
    })

    const total = await prisma.project.count({ where })

    return NextResponse.json({
      projects,
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total
      }
    })
  } catch (error) {
    console.error('Get projects error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const data = projectSchema.parse(body)

    // Check subscription limits
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        subscription: true,
        projects: { where: { status: { not: 'ARCHIVED' } } }
      }
    })

    if (user?.subscription?.tier === 'FREE' && user.projects.length >= 1) {
      return NextResponse.json(
        { error: 'Free plan allows only 1 active project. Please upgrade to create more.' },
        { status: 403 }
      )
    }

    const shareToken = generateShareToken()

    const project = await prisma.project.create({
      data: {
        name: data.projectName,
        clientName: data.clientName,
        clientEmail: data.clientEmail,
        description: data.description,
        status: data.status || 'DRAFT',
        shareToken,
        userId: session.user.id,
        branding: data.branding ? JSON.stringify(data.branding) : null,
        formFields: {
          create: data.formFields?.map((field, index) => ({
            ...field,
            order: index
          })) || []
        },
        checklistItems: {
          create: data.checklistItems?.map((item, index) => ({
            ...item,
            order: index
          })) || []
        }
      },
      include: {
        formFields: true,
        checklistItems: true
      }
    })

    return NextResponse.json({ project, shareToken }, { status: 201 })
  } catch (error) {
    console.error('Create project error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}