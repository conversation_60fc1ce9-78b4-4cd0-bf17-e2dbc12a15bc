// app/api/portal/[token]/submit/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { sendEmail } from '@/lib/email'

export async function POST(
  request: NextRequest,
  { params }: { params: { token: string } }
) {
  try {
    const body = await request.json()
    const { formData, completedTasks, uploadedFiles, status } = body

    const project = await prisma.project.findFirst({
      where: {
        shareToken: params.token,
        status: { in: ['SHARED', 'IN_PROGRESS', 'COMPLETED'] }
      },
      include: {
        user: {
          select: {
            name: true,
            email: true
          }
        }
      }
    })

    if (!project) {
      return NextResponse.json(
        { error: 'Portal not found or not accessible' },
        { status: 404 }
      )
    }

    // Update or create client data
    const clientData = await prisma.clientData.upsert({
      where: { projectId: project.id },
      update: {
        formData: JSON.stringify(formData),
        completedTasks: completedTasks,
        uploadedFiles: JSON.stringify(uploadedFiles),
        submittedAt: status === 'submitted' ? new Date() : null,
        updatedAt: new Date()
      },
      create: {
        projectId: project.id,
        formData: JSON.stringify(formData),
        completedTasks: completedTasks,
        uploadedFiles: JSON.stringify(uploadedFiles),
        submittedAt: status === 'submitted' ? new Date() : null
      }
    })

    // Update project status
    let newProjectStatus = project.status
    if (status === 'submitted') {
      newProjectStatus = 'COMPLETED'
    } else if (status === 'in_progress' && project.status === 'SHARED') {
      newProjectStatus = 'IN_PROGRESS'
    }

    if (newProjectStatus !== project.status) {
      await prisma.project.update({
        where: { id: project.id },
        data: { status: newProjectStatus }
      })
    }

    // Send notification email to freelancer if submitted
    if (status === 'submitted') {
      try {
        await sendEmail({
          to: project.user.email,
          subject: `Client Onboarding Completed - ${project.name}`,
          template: 'client_submission',
          data: {
            freelancerName: project.user.name,
            clientName: project.clientName,
            projectName: project.name,
            portalUrl: `${process.env.NEXTAUTH_URL}/portal/${params.token}`
          }
        })
      } catch (emailError) {
        console.error('Failed to send notification email:', emailError)
        // Don't fail the submission if email fails
      }
    }

    return NextResponse.json({
      message: status === 'submitted' ? 'Submission completed successfully' : 'Progress saved',
      clientData,
      projectStatus: newProjectStatus
    })
  } catch (error) {
    console.error('Portal submission error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}