// app/api/portal/[token]/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: { token: string } }
) {
  try {
    const project = await prisma.project.findFirst({
      where: {
        shareToken: params.token,
        status: { in: ['SHARED', 'IN_PROGRESS', 'COMPLETED'] }
      },
      include: {
        formFields: { orderBy: { order: 'asc' } },
        checklistItems: { orderBy: { order: 'asc' } },
        clientData: true,
        user: {
          select: {
            name: true,
            email: true
          }
        }
      }
    })

    if (!project) {
      return NextResponse.json(
        { error: 'Portal not found or not accessible' },
        { status: 404 }
      )
    }

    // Parse branding JSON
    const portalData = {
      id: project.id,
      name: project.name,
      clientName: project.clientName,
      clientEmail: project.clientEmail,
      description: project.description,
      status: project.status,
      branding: project.branding ? JSON.parse(project.branding as string) : null,
      formFields: project.formFields,
      checklistItems: project.checklistItems,
      clientData: project.clientData,
      freelancer: project.user,
      createdAt: project.createdAt,
      updatedAt: project.updatedAt
    }

    return NextResponse.json(portalData)
  } catch (error) {
    console.error('Get portal error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}