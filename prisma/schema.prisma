// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          UserRole  @default(USER)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts      Account[]
  sessions      Session[]
  projects      Project[]
  tasks         Task[]
  files         File[]
  subscriptions Subscription[]
  notifications Notification[]
  clientProjects ClientProject[]

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Project {
  id          String        @id @default(cuid())
  name        String
  description String?
  status      ProjectStatus @default(DRAFT)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  userId      String
  shareToken  String?       @unique

  user          User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  tasks         Task[]
  files         File[]
  workflows     Workflow[]
  clientProject ClientProject[]

  @@map("projects")
}

model ClientProject {
  id        String              @id @default(cuid())
  projectId String
  clientId  String
  status    ClientProjectStatus @default(NOT_STARTED)
  progress  Int                 @default(0)
  createdAt DateTime            @default(now())
  updatedAt DateTime            @updatedAt

  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  client  User    @relation(fields: [clientId], references: [id], onDelete: Cascade)

  @@unique([projectId, clientId])
  @@map("client_projects")
}

model Task {
  id          String     @id @default(cuid())
  title       String
  description String?
  status      TaskStatus @default(TODO)
  priority    Priority   @default(MEDIUM)
  dueDate     DateTime?
  completedAt DateTime?
  order       Int        @default(0)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  projectId   String
  userId      String?

  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user    User?   @relation(fields: [userId], references: [id], onDelete: SetNull)
  files   File[]

  @@map("tasks")
}

model File {
  id        String   @id @default(cuid())
  name      String
  path      String
  size      Int
  mimeType  String
  createdAt DateTime @default(now())
  userId    String
  projectId String?
  taskId    String?

  user    User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  project Project? @relation(fields: [projectId], references: [id], onDelete: SetNull)
  task    Task?    @relation(fields: [taskId], references: [id], onDelete: SetNull)

  @@map("files")
}

model Workflow {
  id          String        @id @default(cuid())
  name        String
  description String?
  steps       Json // Array of workflow steps
  isActive    Boolean       @default(true)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  projectId   String
  type        WorkflowType  @default(ONBOARDING)

  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("workflows")
}

model Subscription {
  id                String             @id @default(cuid())
  userId            String
  tier              SubscriptionTier   @default(FREE)
  stripeCustomerId  String?            @unique
  stripeSubscriptionId String?         @unique
  stripePriceId     String?
  status            SubscriptionStatus @default(ACTIVE)
  currentPeriodStart DateTime?
  currentPeriodEnd  DateTime?
  cancelAtPeriodEnd Boolean           @default(false)
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
}

model Notification {
  id        String           @id @default(cuid())
  title     String
  message   String
  type      NotificationType @default(INFO)
  isRead    Boolean          @default(false)
  createdAt DateTime         @default(now())
  userId    String

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

enum UserRole {
  USER
  ADMIN
}

enum ProjectStatus {
  DRAFT
  ACTIVE
  COMPLETED
  ARCHIVED
}

enum ClientProjectStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  ON_HOLD
}

enum TaskStatus {
  TODO
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum WorkflowType {
  ONBOARDING
  PROJECT_DELIVERY
  CUSTOM
}

enum SubscriptionTier {
  FREE
  BASIC
  PRO
  ENTERPRISE
}

enum SubscriptionStatus {
  INACTIVE
  ACTIVE
  CANCELLED
  PAST_DUE
}

enum NotificationType {
  INFO
  SUCCESS
  WARNING
  ERROR
}