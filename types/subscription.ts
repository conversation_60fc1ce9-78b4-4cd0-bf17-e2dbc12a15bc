// types/subscription.ts
export interface SubscriptionTier {
  name: string
  price: number
  priceId: string | null
  features: string[]
}

export interface SubscriptionLimits {
  maxProjects: number
  maxTemplates: number
  customBranding: boolean
  analytics: boolean
  customDomain: boolean
  teamMembers: number
}

export interface StripeSubscription {
  id: string
  customer: string
  status: string
  current_period_start: number
  current_period_end: number
  cancel_at_period_end: boolean
}

export interface BillingPortalSession {
  url: string
}