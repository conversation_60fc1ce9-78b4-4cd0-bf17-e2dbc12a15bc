// types/database.ts
export interface DatabaseUser {
  id: string
  email: string
  name: string
  password?: string
  image?: string
  subscriptionTier: 'FREE' | 'PRO' | 'BUSINESS'
  emailVerified?: Date
  createdAt: Date
  updatedAt: Date
}

export interface DatabaseProject {
  id: string
  name: string
  clientName: string
  clientEmail: string
  status: 'DRAFT' | 'SHARED' | 'IN_PROGRESS' | 'COMPLETED' | 'ARCHIVED'
  templateId: string
  shareToken: string
  organizationId: string
  createdBy: string
  description?: string
  startDate?: Date
  duration?: string
  clientData?: any
  createdAt: Date
  updatedAt: Date
}

export interface DatabaseTemplate {
  id: string
  name: string
  description: string
  category: string
  formFields: any
  checklistItems: any
  isPublic: boolean
  createdBy: string
  usageCount: number
  createdAt: Date
  updatedAt: Date
}

export interface DatabaseOrganization {
  id: string
  name: string
  logoUrl?: string
  primaryColor: string
  secondaryColor: string
  customDomain?: string
  ownerId: string
  createdAt: Date
  updatedAt: Date
}