// lib/validations.ts
import { z } from 'zod'

export const registerSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
})

export const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
})

export const projectSchema = z.object({
  name: z.string().min(1, 'Project name is required'),
  clientName: z.string().min(1, 'Client name is required'),
  clientEmail: z.string().email('Invalid client email'),
  templateId: z.string().min(1, 'Template is required'),
  description: z.string().optional(),
  startDate: z.string().optional(),
  duration: z.string().optional(),
})

export const formFieldSchema = z.object({
  id: z.string(),
  label: z.string().min(1, 'Label is required'),
  type: z.enum(['text', 'email', 'phone', 'select', 'multiselect', 'file', 'date', 'textarea']),
  required: z.boolean(),
  options: z.array(z.string()).optional(),
  order: z.number(),
})

export const checklistItemSchema = z.object({
  id: z.string(),
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  assigneeType: z.enum(['freelancer', 'client', 'both']),
  order: z.number(),
})

export const brandingSchema = z.object({
  logoUrl: z.string().url().optional(),
  primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
  secondaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
  customDomain: z.string().optional(),
})