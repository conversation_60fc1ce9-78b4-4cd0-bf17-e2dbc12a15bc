// lib/types.ts
export interface User {
  id: string
  email: string
  name: string
  image?: string
  subscriptionTier: 'FREE' | 'PRO' | 'BUSINESS'
  createdAt: Date
  updatedAt: Date
}

export interface Organization {
  id: string
  name: string
  logoUrl?: string
  primaryColor: string
  secondaryColor: string
  customDomain?: string
  ownerId: string
  createdAt: Date
  updatedAt: Date
}

export interface Project {
  id: string
  name: string
  clientName: string
  clientEmail: string
  status: 'DRAFT' | 'SHARED' | 'IN_PROGRESS' | 'COMPLETED' | 'ARCHIVED'
  templateId: string
  shareToken: string
  organizationId: string
  createdBy: string
  description?: string
  startDate?: string
  duration?: string
  createdAt: Date
  updatedAt: Date
  formFields: FormField[]
  checklistItems: ChecklistItem[]
  files: File[]
}

export interface Template {
  id: string
  name: string
  description: string
  category: string
  formFields: FormField[]
  checklistItems: ChecklistItem[]
  isPublic: boolean
  createdBy: string
  usageCount: number
  createdAt: Date
  updatedAt: Date
}

export interface FormField {
  id: string
  label: string
  type: 'TEXT' | 'EMAIL' | 'PHONE' | 'SELECT' | 'MULTISELECT' | 'FILE_UPLOAD' | 'DATE' | 'TEXTAREA'
  required: boolean
  options?: string[]
  order: number
  value?: string
}

export interface ChecklistItem {
  id: string
  title: string
  description?: string
  completed: boolean
  completedBy?: string
  completedAt?: Date
  assigneeType: 'FREELANCER' | 'CLIENT' | 'BOTH'
  order: number
}

export interface FileUpload {
  id: string
  filename: string
  size: number
  mimeType: string
  storageProvider: string
  storagePath: string
  uploadedBy: string
  uploadedAt: Date
  downloadUrl: string
}

export interface Subscription {
  id: string
  userId: string
  stripeCustomerId: string
  stripeSubscriptionId: string
  tier: 'FREE' | 'PRO' | 'BUSINESS'
  status: 'ACTIVE' | 'CANCELED' | 'PAST_DUE'
  currentPeriodStart: Date
  currentPeriodEnd: Date
  cancelAtPeriodEnd: boolean
}