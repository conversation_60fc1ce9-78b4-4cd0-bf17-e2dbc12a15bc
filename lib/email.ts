// lib/email.ts
import nodemailer from 'nodemailer'

const transporter = nodemailer.createTransporter({
  host: process.env.SMTP_HOST,
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
})

export async function sendEmail({
  to,
  subject,
  html,
}: {
  to: string
  subject: string
  html: string
}) {
  try {
    await transporter.sendMail({
      from: process.env.FROM_EMAIL,
      to,
      subject,
      html,
    })
  } catch (error) {
    console.error('Email sending failed:', error)
    throw error
  }
}

export const emailTemplates = {
  welcome: (name: string) => ({
    subject: 'Welcome to Client Onboarding Assistant!',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Welcome ${name}!</h1>
        <p>Thank you for joining Client Onboarding Assistant. You're now ready to create professional onboarding portals for your clients.</p>
        <p>Get started by creating your first project.</p>
      </div>
    `
  }),
  
  clientInvitation: (clientName: string, portalUrl: string, freelancerName: string) => ({
    subject: 'Complete Your Onboarding Process',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Hello ${clientName}!</h1>
        <p>${freelancerName} has created a personalized onboarding portal for you.</p>
        <p>Please complete the required information to get started with your project.</p>
        <a href="${portalUrl}" style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0;">Access Your Portal</a>
      </div>
    `
  }),
  
  reminder: (clientName: string, portalUrl: string) => ({
    subject: 'Reminder: Complete Your Onboarding',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Hi ${clientName}!</h1>
        <p>This is a friendly reminder that your onboarding process is still pending.</p>
        <p>Please take a moment to complete the required information.</p>
        <a href="${portalUrl}" style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0;">Complete Onboarding</a>
      </div>
    `
  })
}