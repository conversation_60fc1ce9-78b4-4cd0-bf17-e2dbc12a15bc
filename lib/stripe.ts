// lib/stripe.ts
import Stripe from 'stripe'

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not set')
}

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
})

export const STRIPE_PLANS = {
  FREE: {
    name: 'Free',
    price: 0,
    priceId: null,
    features: ['1 active project', '5 templates', 'Basic branding']
  },
  PRO: {
    name: 'Pro',
    price: 10,
    priceId: process.env.STRIPE_PRO_PRICE_ID,
    features: ['Unlimited projects', '50+ templates', 'Custom branding', 'Analytics']
  },
  BUSINESS: {
    name: 'Business',
    price: 29,
    priceId: process.env.STRIPE_BUSINESS_PRICE_ID,
    features: ['Team management', 'Custom domains', 'API access', 'Priority support']
  }
}