// lib/constants.ts
export const SUBSCRIPTION_TIERS = {
  FREE: 'FREE',
  PRO: 'PRO',
  BUSINESS: 'BUSINESS',
} as const

export const PROJECT_STATUS = {
  DRAFT: 'DRAFT',
  SHARED: 'SHARED',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  ARCHIVED: 'ARCHIVED',
} as const

export const FIELD_TYPES = {
  TEXT: 'TEXT',
  EMAIL: 'EMAIL',
  PHONE: 'PHONE',
  SELECT: 'SELECT',
  MULTISELECT: 'MULTISELECT',
  FILE_UPLOAD: 'FILE_UPLOAD',
  DATE: 'DATE',
  TEXTAREA: 'TEXTAREA',
} as const

export const ASSIGNEE_TYPES = {
  FREELANCER: 'FREELANCER',
  CLIENT: 'CLIENT',
  BOTH: 'BOTH',
} as const

export const TEMPLATE_CATEGORIES = {
  DEVELOPMENT: 'DEVELOPMENT',
  DESIGN: 'DESIGN',
  COACHING: 'COACHING',
  MARKETING: 'MARKETING',
  CONSULTING: 'CONSULTING',
  PHOTOGRAPHY: 'PHOTOGRAPHY',
  WRITING: 'WRITING',
} as const

export const DEFAULT_TEMPLATES = [
  {
    id: 'dev-template',
    name: 'Web Development',
    category: TEMPLATE_CATEGORIES.DEVELOPMENT,
    description: 'Perfect for web development projects',
    formFields: [
      { id: '1', label: 'Project Requirements', type: 'textarea', required: true, order: 1 },
      { id: '2', label: 'Technical Stack Preferences', type: 'multiselect', required: false, order: 2, options: ['React', 'Vue', 'Angular', 'Node.js', 'Python', 'PHP'] },
      { id: '3', label: 'Domain & Hosting Access', type: 'textarea', required: true, order: 3 },
      { id: '4', label: 'Design Files', type: 'file', required: false, order: 4 },
    ],
    checklistItems: [
      { id: '1', title: 'Provide project requirements', assigneeType: 'client', order: 1 },
      { id: '2', title: 'Share hosting credentials', assigneeType: 'client', order: 2 },
      { id: '3', title: 'Set up development environment', assigneeType: 'freelancer', order: 3 },
      { id: '4', title: 'Create project timeline', assigneeType: 'freelancer', order: 4 },
    ]
  },
  {
    id: 'design-template',
    name: 'UI/UX Design',
    category: TEMPLATE_CATEGORIES.DESIGN,
    description: 'Ideal for design projects',
    formFields: [
      { id: '1', label: 'Brand Guidelines', type: 'file', required: false, order: 1 },
      { id: '2', label: 'Target Audience', type: 'textarea', required: true, order: 2 },
      { id: '3', label: 'Design Preferences', type: 'textarea', required: true, order: 3 },
      { id: '4', label: 'Competitor References', type: 'textarea', required: false, order: 4 },
    ],
    checklistItems: [
      { id: '1', title: 'Provide brand assets', assigneeType: 'client', order: 1 },
      { id: '2', title: 'Complete design brief', assigneeType: 'client', order: 2 },
      { id: '3', title: 'Create wireframes', assigneeType: 'freelancer', order: 3 },
      { id: '4', title: 'Present initial concepts', assigneeType: 'freelancer', order: 4 },
    ]
  },
  {
    id: 'coaching-template',
    name: 'Business Coaching',
    category: TEMPLATE_CATEGORIES.COACHING,
    description: 'For coaching and consulting services',
    formFields: [
      { id: '1', label: 'Current Business Situation', type: 'textarea', required: true, order: 1 },
      { id: '2', label: 'Goals & Objectives', type: 'textarea', required: true, order: 2 },
      { id: '3', label: 'Preferred Communication Method', type: 'select', required: true, order: 3, options: ['Video Calls', 'Phone Calls', 'In-Person', 'Email'] },
      { id: '4', label: 'Availability', type: 'textarea', required: true, order: 4 },
    ],
    checklistItems: [
      { id: '1', title: 'Complete intake assessment', assigneeType: 'client', order: 1 },
      { id: '2', title: 'Schedule initial consultation', assigneeType: 'both', order: 2 },
      { id: '3', title: 'Create coaching plan', assigneeType: 'freelancer', order: 3 },
      { id: '4', title: 'Set up regular check-ins', assigneeType: 'freelancer', order: 4 },
    ]
  },
  {
    id: 'marketing-template',
    name: 'Digital Marketing',
    category: TEMPLATE_CATEGORIES.MARKETING,
    description: 'For marketing and advertising projects',
    formFields: [
      { id: '1', label: 'Business Overview', type: 'textarea', required: true, order: 1 },
      { id: '2', label: 'Target Market', type: 'textarea', required: true, order: 2 },
      { id: '3', label: 'Marketing Budget', type: 'text', required: true, order: 3 },
      { id: '4', label: 'Current Marketing Channels', type: 'multiselect', required: false, order: 4, options: ['Social Media', 'Email', 'PPC', 'SEO', 'Content Marketing', 'Influencer Marketing'] },
    ],
    checklistItems: [
      { id: '1', title: 'Provide business information', assigneeType: 'client', order: 1 },
      { id: '2', title: 'Share marketing assets', assigneeType: 'client', order: 2 },
      { id: '3', title: 'Conduct market analysis', assigneeType: 'freelancer', order: 3 },
      { id: '4', title: 'Develop marketing strategy', assigneeType: 'freelancer', order: 4 },
    ]
  },
  {
    id: 'consulting-template',
    name: 'Business Consulting',
    category: TEMPLATE_CATEGORIES.CONSULTING,
    description: 'For general consulting services',
    formFields: [
      { id: '1', label: 'Project Scope', type: 'textarea', required: true, order: 1 },
      { id: '2', label: 'Current Challenges', type: 'textarea', required: true, order: 2 },
      { id: '3', label: 'Expected Outcomes', type: 'textarea', required: true, order: 3 },
      { id: '4', label: 'Timeline Requirements', type: 'text', required: true, order: 4 },
    ],
    checklistItems: [
      { id: '1', title: 'Define project scope', assigneeType: 'both', order: 1 },
      { id: '2', title: 'Provide company information', assigneeType: 'client', order: 2 },
      { id: '3', title: 'Conduct initial analysis', assigneeType: 'freelancer', order: 3 },
      { id: '4', title: 'Present findings', assigneeType: 'freelancer', order: 4 },
    ]
  }
]

export const SUBSCRIPTION_LIMITS = {
  [SUBSCRIPTION_TIERS.FREE]: {
    maxProjects: 1,
    maxTemplates: 5,
    customBranding: false,
    analytics: false,
    customDomain: false,
    teamMembers: 1,
  },
  [SUBSCRIPTION_TIERS.PRO]: {
    maxProjects: -1, // unlimited
    maxTemplates: 50,
    customBranding: true,
    analytics: true,
    customDomain: false,
    teamMembers: 1,
  },
  [SUBSCRIPTION_TIERS.BUSINESS]: {
    maxProjects: -1, // unlimited
    maxTemplates: -1, // unlimited
    customBranding: true,
    analytics: true,
    customDomain: true,
    teamMembers: 10,
  },
}