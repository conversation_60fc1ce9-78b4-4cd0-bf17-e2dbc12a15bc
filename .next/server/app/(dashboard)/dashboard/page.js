/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(dashboard)/dashboard/page";
exports.ids = ["app/(dashboard)/dashboard/page"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "@prisma/client/scripts/default-index.js":
/*!**********************************************************!*\
  !*** external "@prisma/client/scripts/default-index.js" ***!
  \**********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client/scripts/default-index.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(dashboard)%2Fdashboard%2Fpage&page=%2F(dashboard)%2Fdashboard%2Fpage&appPaths=%2F(dashboard)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(dashboard)%2Fdashboard%2Fpage&page=%2F(dashboard)%2Fdashboard%2Fpage&appPaths=%2F(dashboard)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(dashboard)',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(dashboard)/dashboard/page.tsx */ \"(rsc)/./app/(dashboard)/dashboard/page.tsx\")), \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(dashboard)/layout.tsx */ \"(rsc)/./app/(dashboard)/layout.tsx\")), \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/(dashboard)/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(dashboard)/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(dashboard)%2Fdashboard%2Fpage&page=%2F(dashboard)%2Fdashboard%2Fpage&appPaths=%2F(dashboard)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fapp%2F(dashboard)%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fapp%2F(dashboard)%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(dashboard)/dashboard/page.tsx */ \"(ssr)/./app/(dashboard)/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXNhbGFrbyUyRkRlc2t0b3AlMkZQUk9KRVRTJTJGV2ViJTJGY2xpZW50LW9uYm9hcmRpbmctYXNzaXN0YW50JTJGYXBwJTJGKGRhc2hib2FyZCklMkZkYXNoYm9hcmQlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0xBQXNJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2xpZW50LW9uYm9hcmRpbmctYXNzaXN0YW50Lz8xNWEyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FzYWxha28vRGVza3RvcC9QUk9KRVRTL1dlYi9jbGllbnQtb25ib2FyZGluZy1hc3Npc3RhbnQvYXBwLyhkYXNoYm9hcmQpL2Rhc2hib2FyZC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fapp%2F(dashboard)%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fcomponents%2Flayout%2Fmain-layout.tsx%22%2C%22ids%22%3A%5B%22MainLayout%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fcomponents%2Flayout%2Fmain-layout.tsx%22%2C%22ids%22%3A%5B%22MainLayout%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/layout/main-layout.tsx */ \"(ssr)/./components/layout/main-layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXNhbGFrbyUyRkRlc2t0b3AlMkZQUk9KRVRTJTJGV2ViJTJGY2xpZW50LW9uYm9hcmRpbmctYXNzaXN0YW50JTJGY29tcG9uZW50cyUyRmxheW91dCUyRm1haW4tbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMk1haW5MYXlvdXQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtMQUFxSyIsInNvdXJjZXMiOlsid2VicGFjazovL2NsaWVudC1vbmJvYXJkaW5nLWFzc2lzdGFudC8/ZmIxNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIk1haW5MYXlvdXRcIl0gKi8gXCIvVXNlcnMvYXNhbGFrby9EZXNrdG9wL1BST0pFVFMvV2ViL2NsaWVudC1vbmJvYXJkaW5nLWFzc2lzdGFudC9jb21wb25lbnRzL2xheW91dC9tYWluLWxheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fcomponents%2Flayout%2Fmain-layout.tsx%22%2C%22ids%22%3A%5B%22MainLayout%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/(dashboard)/dashboard/page.tsx":
/*!********************************************!*\
  !*** ./app/(dashboard)/dashboard/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_dashboard_stats_cards__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/stats-cards */ \"(ssr)/./components/dashboard/stats-cards.tsx\");\n/* harmony import */ var _components_dashboard_quick_actions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/quick-actions */ \"(ssr)/./components/dashboard/quick-actions.tsx\");\n/* harmony import */ var _components_dashboard_projects_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/projects-list */ \"(ssr)/./components/dashboard/projects-list.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_loading_spinner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/loading-spinner */ \"(ssr)/./components/ui/loading-spinner.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n// app/(dashboard)/dashboard/page.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalProjects: 0,\n        activeProjects: 0,\n        completedProjects: 0,\n        totalClients: 0,\n        pendingResponses: 0,\n        completionRate: 0,\n        avgCompletionTime: 0\n    });\n    const [projects, setProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [templates, setTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (session) {\n            loadDashboardData();\n        }\n    }, [\n        session\n    ]);\n    const loadDashboardData = async ()=>{\n        try {\n            // Load projects\n            const projectsResponse = await fetch(\"/api/projects?limit=5\");\n            if (projectsResponse.ok) {\n                const projectsData = await projectsResponse.json();\n                setProjects(projectsData.projects);\n                // Calculate stats\n                const total = projectsData.projects.length;\n                const active = projectsData.projects.filter((p)=>p.status === \"IN_PROGRESS\").length;\n                const completed = projectsData.projects.filter((p)=>p.status === \"COMPLETED\").length;\n                const pending = projectsData.projects.filter((p)=>p.status === \"SHARED\").length;\n                setStats({\n                    totalProjects: total,\n                    activeProjects: active,\n                    completedProjects: completed,\n                    totalClients: projectsData.projects.length,\n                    pendingResponses: pending,\n                    completionRate: total > 0 ? Math.round(completed / total * 100) : 0,\n                    avgCompletionTime: 7 // Mock data\n                });\n            }\n            // Load templates (mock data for now)\n            setTemplates([\n                {\n                    id: \"1\",\n                    name: \"Web Development\",\n                    category: \"Development\"\n                },\n                {\n                    id: \"2\",\n                    name: \"Brand Identity\",\n                    category: \"Design\"\n                },\n                {\n                    id: \"3\",\n                    name: \"Marketing Strategy\",\n                    category: \"Marketing\"\n                }\n            ]);\n        } catch (err) {\n            setError(\"Failed to load dashboard data\");\n            console.error(\"Dashboard error:\", err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleProjectStatusChange = async (project)=>{\n        // This function signature matches the component expectation\n        console.log(\"Status change for project:\", project.id);\n        loadDashboardData() // Reload dashboard data\n        ;\n    };\n    const handleProjectDelete = async (project)=>{\n        if (!confirm(\"Are you sure you want to delete this project?\")) return;\n        try {\n            const response = await fetch(`/api/projects/${project.id}`, {\n                method: \"DELETE\"\n            });\n            if (response.ok) {\n                loadDashboardData() // Reload dashboard data\n                ;\n            }\n        } catch (err) {\n            console.error(\"Delete error:\", err);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loading_spinner__WEBPACK_IMPORTED_MODULE_7__.LoadingSpinner, {\n                size: \"lg\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900\",\n                        children: [\n                            \"Welcome back, \",\n                            session?.user?.name,\n                            \"!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mt-2\",\n                        children: \"Here's what's happening with your client onboarding projects\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                variant: \"destructive\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats_cards__WEBPACK_IMPORTED_MODULE_3__.StatsCards, {\n                stats: stats\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Quick Actions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_quick_actions__WEBPACK_IMPORTED_MODULE_4__.QuickActions, {\n                            recentProjects: projects.slice(0, 3),\n                            templates: templates\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                            children: \"Recent Projects\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_projects_list__WEBPACK_IMPORTED_MODULE_5__.ProjectsList, {\n                            projects: projects,\n                            onEdit: (project)=>router.push(`/projects/${project.id}`),\n                            onDelete: handleProjectDelete,\n                            onView: (project)=>router.push(`/portal/${project.shareToken || project.id}`)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvKGRhc2hib2FyZCkvZGFzaGJvYXJkL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxxQ0FBcUM7O0FBR007QUFDQztBQUNtQjtBQUNJO0FBQ0E7QUFDWTtBQUNmO0FBQ0Q7QUFDUDtBQUNiO0FBRTVCLFNBQVNnQjtJQUN0QixNQUFNLEVBQUVDLE1BQU1DLE9BQU8sRUFBRSxHQUFHaEIsMkRBQVVBO0lBQ3BDLE1BQU1pQixTQUFTSiwwREFBU0E7SUFDeEIsTUFBTSxDQUFDSyxPQUFPQyxTQUFTLEdBQUdyQiwrQ0FBUUEsQ0FBQztRQUNqQ3NCLGVBQWU7UUFDZkMsZ0JBQWdCO1FBQ2hCQyxtQkFBbUI7UUFDbkJDLGNBQWM7UUFDZEMsa0JBQWtCO1FBQ2xCQyxnQkFBZ0I7UUFDaEJDLG1CQUFtQjtJQUNyQjtJQUNBLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHOUIsK0NBQVFBLENBQUMsRUFBRTtJQUMzQyxNQUFNLENBQUMrQixXQUFXQyxhQUFhLEdBQUdoQywrQ0FBUUEsQ0FBQyxFQUFFO0lBQzdDLE1BQU0sQ0FBQ2lDLFNBQVNDLFdBQVcsR0FBR2xDLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ21DLE9BQU9DLFNBQVMsR0FBR3BDLCtDQUFRQSxDQUFDO0lBRW5DQyxnREFBU0EsQ0FBQztRQUNSLElBQUlpQixTQUFTO1lBQ1htQjtRQUNGO0lBQ0YsR0FBRztRQUFDbkI7S0FBUTtJQUVaLE1BQU1tQixvQkFBb0I7UUFDeEIsSUFBSTtZQUNGLGdCQUFnQjtZQUNoQixNQUFNQyxtQkFBbUIsTUFBTUMsTUFBTTtZQUNyQyxJQUFJRCxpQkFBaUJFLEVBQUUsRUFBRTtnQkFDdkIsTUFBTUMsZUFBZSxNQUFNSCxpQkFBaUJJLElBQUk7Z0JBQ2hEWixZQUFZVyxhQUFhWixRQUFRO2dCQUVqQyxrQkFBa0I7Z0JBQ2xCLE1BQU1jLFFBQVFGLGFBQWFaLFFBQVEsQ0FBQ2UsTUFBTTtnQkFDMUMsTUFBTUMsU0FBU0osYUFBYVosUUFBUSxDQUFDaUIsTUFBTSxDQUFDLENBQUNDLElBQVdBLEVBQUVDLE1BQU0sS0FBSyxlQUFlSixNQUFNO2dCQUMxRixNQUFNSyxZQUFZUixhQUFhWixRQUFRLENBQUNpQixNQUFNLENBQUMsQ0FBQ0MsSUFBV0EsRUFBRUMsTUFBTSxLQUFLLGFBQWFKLE1BQU07Z0JBQzNGLE1BQU1NLFVBQVVULGFBQWFaLFFBQVEsQ0FBQ2lCLE1BQU0sQ0FBQyxDQUFDQyxJQUFXQSxFQUFFQyxNQUFNLEtBQUssVUFBVUosTUFBTTtnQkFFdEZ2QixTQUFTO29CQUNQQyxlQUFlcUI7b0JBQ2ZwQixnQkFBZ0JzQjtvQkFDaEJyQixtQkFBbUJ5QjtvQkFDbkJ4QixjQUFjZ0IsYUFBYVosUUFBUSxDQUFDZSxNQUFNO29CQUMxQ2xCLGtCQUFrQndCO29CQUNsQnZCLGdCQUFnQmdCLFFBQVEsSUFBSVEsS0FBS0MsS0FBSyxDQUFDLFlBQWFULFFBQVMsT0FBTztvQkFDcEVmLG1CQUFtQixFQUFFLFlBQVk7Z0JBQ25DO1lBQ0Y7WUFFQSxxQ0FBcUM7WUFDckNJLGFBQWE7Z0JBQ1g7b0JBQUVxQixJQUFJO29CQUFLQyxNQUFNO29CQUFtQkMsVUFBVTtnQkFBYztnQkFDNUQ7b0JBQUVGLElBQUk7b0JBQUtDLE1BQU07b0JBQWtCQyxVQUFVO2dCQUFTO2dCQUN0RDtvQkFBRUYsSUFBSTtvQkFBS0MsTUFBTTtvQkFBc0JDLFVBQVU7Z0JBQVk7YUFDOUQ7UUFDSCxFQUFFLE9BQU9DLEtBQUs7WUFDWnBCLFNBQVM7WUFDVHFCLFFBQVF0QixLQUFLLENBQUMsb0JBQW9CcUI7UUFDcEMsU0FBVTtZQUNSdEIsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNd0IsNEJBQTRCLE9BQU9DO1FBQ3ZDLDREQUE0RDtRQUM1REYsUUFBUUcsR0FBRyxDQUFDLDhCQUE4QkQsUUFBUU4sRUFBRTtRQUNwRGhCLG9CQUFvQix3QkFBd0I7O0lBQzlDO0lBRUEsTUFBTXdCLHNCQUFzQixPQUFPRjtRQUNqQyxJQUFJLENBQUNHLFFBQVEsa0RBQWtEO1FBRS9ELElBQUk7WUFDRixNQUFNQyxXQUFXLE1BQU14QixNQUFNLENBQUMsY0FBYyxFQUFFb0IsUUFBUU4sRUFBRSxDQUFDLENBQUMsRUFBRTtnQkFDMURXLFFBQVE7WUFDVjtZQUVBLElBQUlELFNBQVN2QixFQUFFLEVBQUU7Z0JBQ2ZILG9CQUFvQix3QkFBd0I7O1lBQzlDO1FBQ0YsRUFBRSxPQUFPbUIsS0FBSztZQUNaQyxRQUFRdEIsS0FBSyxDQUFDLGlCQUFpQnFCO1FBQ2pDO0lBQ0Y7SUFFQSxJQUFJdkIsU0FBUztRQUNYLHFCQUNFLDhEQUFDZ0M7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ3hELDBFQUFjQTtnQkFBQ3lELE1BQUs7Ozs7Ozs7Ozs7O0lBRzNCO0lBRUEscUJBQ0UsOERBQUNGO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDs7a0NBQ0MsOERBQUNHO3dCQUFHRixXQUFVOzs0QkFBbUM7NEJBQ2hDaEQsU0FBU21ELE1BQU1mOzRCQUFLOzs7Ozs7O2tDQUVyQyw4REFBQ1A7d0JBQUVtQixXQUFVO2tDQUFxQjs7Ozs7Ozs7Ozs7O1lBS25DL0IsdUJBQ0MsOERBQUN4Qix1REFBS0E7Z0JBQUMyRCxTQUFROztrQ0FDYiw4REFBQ3pELHFHQUFhQTt3QkFBQ3FELFdBQVU7Ozs7OztrQ0FDekIsOERBQUN0RCxrRUFBZ0JBO2tDQUFFdUI7Ozs7Ozs7Ozs7OzswQkFLdkIsOERBQUNoQyx5RUFBVUE7Z0JBQUNpQixPQUFPQTs7Ozs7OzBCQUduQiw4REFBQ2QscURBQUlBOztrQ0FDSCw4REFBQ0UsMkRBQVVBO2tDQUNULDRFQUFDQywwREFBU0E7NEJBQUN5RCxXQUFVOzs4Q0FDbkIsOERBQUNwRCxxR0FBVUE7b0NBQUNvRCxXQUFVOzs7Ozs7OENBQ3RCLDhEQUFDSzs4Q0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBR1YsOERBQUNoRSw0REFBV0E7a0NBQ1YsNEVBQUNILDZFQUFZQTs0QkFDWG9FLGdCQUFnQjNDLFNBQVM0QyxLQUFLLENBQUMsR0FBRzs0QkFDbEMxQyxXQUFXQTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTWpCLDhEQUFDekIscURBQUlBOztrQ0FDSCw4REFBQ0UsMkRBQVVBO2tDQUNULDRFQUFDQywwREFBU0E7c0NBQUM7Ozs7Ozs7Ozs7O2tDQUViLDhEQUFDRiw0REFBV0E7a0NBQ1YsNEVBQUNGLDZFQUFZQTs0QkFDWHdCLFVBQVVBOzRCQUNWNkMsUUFBUSxDQUFDZixVQUFZeEMsT0FBT3dELElBQUksQ0FBQyxDQUFDLFVBQVUsRUFBRWhCLFFBQVFOLEVBQUUsQ0FBQyxDQUFDOzRCQUMxRHVCLFVBQVVmOzRCQUNWZ0IsUUFBUSxDQUFDbEIsVUFBWXhDLE9BQU93RCxJQUFJLENBQUMsQ0FBQyxRQUFRLEVBQUVoQixRQUFRbUIsVUFBVSxJQUFJbkIsUUFBUU4sRUFBRSxDQUFDLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTTFGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2xpZW50LW9uYm9hcmRpbmctYXNzaXN0YW50Ly4vYXBwLyhkYXNoYm9hcmQpL2Rhc2hib2FyZC9wYWdlLnRzeD81NzgzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGFwcC8oZGFzaGJvYXJkKS9kYXNoYm9hcmQvcGFnZS50c3hcbid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VTZXNzaW9uIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0J1xuaW1wb3J0IHsgU3RhdHNDYXJkcyB9IGZyb20gJ0AvY29tcG9uZW50cy9kYXNoYm9hcmQvc3RhdHMtY2FyZHMnXG5pbXBvcnQgeyBRdWlja0FjdGlvbnMgfSBmcm9tICdAL2NvbXBvbmVudHMvZGFzaGJvYXJkL3F1aWNrLWFjdGlvbnMnXG5pbXBvcnQgeyBQcm9qZWN0c0xpc3QgfSBmcm9tICdAL2NvbXBvbmVudHMvZGFzaGJvYXJkL3Byb2plY3RzLWxpc3QnXG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnXG5pbXBvcnQgeyBMb2FkaW5nU3Bpbm5lciB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9sb2FkaW5nLXNwaW5uZXInXG5pbXBvcnQgeyBBbGVydCwgQWxlcnREZXNjcmlwdGlvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9hbGVydCdcbmltcG9ydCB7IEFsZXJ0VHJpYW5nbGUsIFRyZW5kaW5nVXAgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERhc2hib2FyZFBhZ2UoKSB7XG4gIGNvbnN0IHsgZGF0YTogc2Vzc2lvbiB9ID0gdXNlU2Vzc2lvbigpXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG4gIGNvbnN0IFtzdGF0cywgc2V0U3RhdHNdID0gdXNlU3RhdGUoe1xuICAgIHRvdGFsUHJvamVjdHM6IDAsXG4gICAgYWN0aXZlUHJvamVjdHM6IDAsXG4gICAgY29tcGxldGVkUHJvamVjdHM6IDAsXG4gICAgdG90YWxDbGllbnRzOiAwLFxuICAgIHBlbmRpbmdSZXNwb25zZXM6IDAsXG4gICAgY29tcGxldGlvblJhdGU6IDAsXG4gICAgYXZnQ29tcGxldGlvblRpbWU6IDBcbiAgfSlcbiAgY29uc3QgW3Byb2plY3RzLCBzZXRQcm9qZWN0c10gPSB1c2VTdGF0ZShbXSlcbiAgY29uc3QgW3RlbXBsYXRlcywgc2V0VGVtcGxhdGVzXSA9IHVzZVN0YXRlKFtdKVxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlKCcnKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHNlc3Npb24pIHtcbiAgICAgIGxvYWREYXNoYm9hcmREYXRhKClcbiAgICB9XG4gIH0sIFtzZXNzaW9uXSlcblxuICBjb25zdCBsb2FkRGFzaGJvYXJkRGF0YSA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgLy8gTG9hZCBwcm9qZWN0c1xuICAgICAgY29uc3QgcHJvamVjdHNSZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3Byb2plY3RzP2xpbWl0PTUnKVxuICAgICAgaWYgKHByb2plY3RzUmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgcHJvamVjdHNEYXRhID0gYXdhaXQgcHJvamVjdHNSZXNwb25zZS5qc29uKClcbiAgICAgICAgc2V0UHJvamVjdHMocHJvamVjdHNEYXRhLnByb2plY3RzKVxuICAgICAgICBcbiAgICAgICAgLy8gQ2FsY3VsYXRlIHN0YXRzXG4gICAgICAgIGNvbnN0IHRvdGFsID0gcHJvamVjdHNEYXRhLnByb2plY3RzLmxlbmd0aFxuICAgICAgICBjb25zdCBhY3RpdmUgPSBwcm9qZWN0c0RhdGEucHJvamVjdHMuZmlsdGVyKChwOiBhbnkpID0+IHAuc3RhdHVzID09PSAnSU5fUFJPR1JFU1MnKS5sZW5ndGhcbiAgICAgICAgY29uc3QgY29tcGxldGVkID0gcHJvamVjdHNEYXRhLnByb2plY3RzLmZpbHRlcigocDogYW55KSA9PiBwLnN0YXR1cyA9PT0gJ0NPTVBMRVRFRCcpLmxlbmd0aFxuICAgICAgICBjb25zdCBwZW5kaW5nID0gcHJvamVjdHNEYXRhLnByb2plY3RzLmZpbHRlcigocDogYW55KSA9PiBwLnN0YXR1cyA9PT0gJ1NIQVJFRCcpLmxlbmd0aFxuICAgICAgICBcbiAgICAgICAgc2V0U3RhdHMoe1xuICAgICAgICAgIHRvdGFsUHJvamVjdHM6IHRvdGFsLFxuICAgICAgICAgIGFjdGl2ZVByb2plY3RzOiBhY3RpdmUsXG4gICAgICAgICAgY29tcGxldGVkUHJvamVjdHM6IGNvbXBsZXRlZCxcbiAgICAgICAgICB0b3RhbENsaWVudHM6IHByb2plY3RzRGF0YS5wcm9qZWN0cy5sZW5ndGgsIC8vIFVzaW5nIHByb2plY3QgY291bnQgYXMgY2xpZW50IGNvdW50IGZvciBub3dcbiAgICAgICAgICBwZW5kaW5nUmVzcG9uc2VzOiBwZW5kaW5nLFxuICAgICAgICAgIGNvbXBsZXRpb25SYXRlOiB0b3RhbCA+IDAgPyBNYXRoLnJvdW5kKChjb21wbGV0ZWQgLyB0b3RhbCkgKiAxMDApIDogMCxcbiAgICAgICAgICBhdmdDb21wbGV0aW9uVGltZTogNyAvLyBNb2NrIGRhdGFcbiAgICAgICAgfSlcbiAgICAgIH1cblxuICAgICAgLy8gTG9hZCB0ZW1wbGF0ZXMgKG1vY2sgZGF0YSBmb3Igbm93KVxuICAgICAgc2V0VGVtcGxhdGVzKFtcbiAgICAgICAgeyBpZDogJzEnLCBuYW1lOiAnV2ViIERldmVsb3BtZW50JywgY2F0ZWdvcnk6ICdEZXZlbG9wbWVudCcgfSxcbiAgICAgICAgeyBpZDogJzInLCBuYW1lOiAnQnJhbmQgSWRlbnRpdHknLCBjYXRlZ29yeTogJ0Rlc2lnbicgfSxcbiAgICAgICAgeyBpZDogJzMnLCBuYW1lOiAnTWFya2V0aW5nIFN0cmF0ZWd5JywgY2F0ZWdvcnk6ICdNYXJrZXRpbmcnIH1cbiAgICAgIF0pXG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBzZXRFcnJvcignRmFpbGVkIHRvIGxvYWQgZGFzaGJvYXJkIGRhdGEnKVxuICAgICAgY29uc29sZS5lcnJvcignRGFzaGJvYXJkIGVycm9yOicsIGVycilcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVQcm9qZWN0U3RhdHVzQ2hhbmdlID0gYXN5bmMgKHByb2plY3Q6IGFueSkgPT4ge1xuICAgIC8vIFRoaXMgZnVuY3Rpb24gc2lnbmF0dXJlIG1hdGNoZXMgdGhlIGNvbXBvbmVudCBleHBlY3RhdGlvblxuICAgIGNvbnNvbGUubG9nKCdTdGF0dXMgY2hhbmdlIGZvciBwcm9qZWN0OicsIHByb2plY3QuaWQpXG4gICAgbG9hZERhc2hib2FyZERhdGEoKSAvLyBSZWxvYWQgZGFzaGJvYXJkIGRhdGFcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVByb2plY3REZWxldGUgPSBhc3luYyAocHJvamVjdDogYW55KSA9PiB7XG4gICAgaWYgKCFjb25maXJtKCdBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gZGVsZXRlIHRoaXMgcHJvamVjdD8nKSkgcmV0dXJuXG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9wcm9qZWN0cy8ke3Byb2plY3QuaWR9YCwge1xuICAgICAgICBtZXRob2Q6ICdERUxFVEUnXG4gICAgICB9KVxuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgbG9hZERhc2hib2FyZERhdGEoKSAvLyBSZWxvYWQgZGFzaGJvYXJkIGRhdGFcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0RlbGV0ZSBlcnJvcjonLCBlcnIpXG4gICAgfVxuICB9XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPExvYWRpbmdTcGlubmVyIHNpemU9XCJsZ1wiIC8+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS04XCI+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdj5cbiAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgV2VsY29tZSBiYWNrLCB7c2Vzc2lvbj8udXNlcj8ubmFtZX0hXG4gICAgICAgIDwvaDE+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbXQtMlwiPlxuICAgICAgICAgIEhlcmUmYXBvcztzIHdoYXQmYXBvcztzIGhhcHBlbmluZyB3aXRoIHlvdXIgY2xpZW50IG9uYm9hcmRpbmcgcHJvamVjdHNcbiAgICAgICAgPC9wPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHtlcnJvciAmJiAoXG4gICAgICAgIDxBbGVydCB2YXJpYW50PVwiZGVzdHJ1Y3RpdmVcIj5cbiAgICAgICAgICA8QWxlcnRUcmlhbmdsZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICA8QWxlcnREZXNjcmlwdGlvbj57ZXJyb3J9PC9BbGVydERlc2NyaXB0aW9uPlxuICAgICAgICA8L0FsZXJ0PlxuICAgICAgKX1cblxuICAgICAgey8qIFN0YXRzIENhcmRzICovfVxuICAgICAgPFN0YXRzQ2FyZHMgc3RhdHM9e3N0YXRzfSAvPlxuXG4gICAgICB7LyogUXVpY2sgQWN0aW9ucyAqL31cbiAgICAgIDxDYXJkPlxuICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgPFRyZW5kaW5nVXAgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICA8c3Bhbj5RdWljayBBY3Rpb25zPC9zcGFuPlxuICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICA8UXVpY2tBY3Rpb25zIFxuICAgICAgICAgICAgcmVjZW50UHJvamVjdHM9e3Byb2plY3RzLnNsaWNlKDAsIDMpfSBcbiAgICAgICAgICAgIHRlbXBsYXRlcz17dGVtcGxhdGVzfVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICA8L0NhcmQ+XG5cbiAgICAgIHsvKiBSZWNlbnQgUHJvamVjdHMgKi99XG4gICAgICA8Q2FyZD5cbiAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRUaXRsZT5SZWNlbnQgUHJvamVjdHM8L0NhcmRUaXRsZT5cbiAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgPFByb2plY3RzTGlzdFxuICAgICAgICAgICAgcHJvamVjdHM9e3Byb2plY3RzfVxuICAgICAgICAgICAgb25FZGl0PXsocHJvamVjdCkgPT4gcm91dGVyLnB1c2goYC9wcm9qZWN0cy8ke3Byb2plY3QuaWR9YCl9XG4gICAgICAgICAgICBvbkRlbGV0ZT17aGFuZGxlUHJvamVjdERlbGV0ZX1cbiAgICAgICAgICAgIG9uVmlldz17KHByb2plY3QpID0+IHJvdXRlci5wdXNoKGAvcG9ydGFsLyR7cHJvamVjdC5zaGFyZVRva2VuIHx8IHByb2plY3QuaWR9YCl9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgIDwvQ2FyZD5cbiAgICA8L2Rpdj5cbiAgKVxufSJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVNlc3Npb24iLCJTdGF0c0NhcmRzIiwiUXVpY2tBY3Rpb25zIiwiUHJvamVjdHNMaXN0IiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkxvYWRpbmdTcGlubmVyIiwiQWxlcnQiLCJBbGVydERlc2NyaXB0aW9uIiwiQWxlcnRUcmlhbmdsZSIsIlRyZW5kaW5nVXAiLCJ1c2VSb3V0ZXIiLCJEYXNoYm9hcmRQYWdlIiwiZGF0YSIsInNlc3Npb24iLCJyb3V0ZXIiLCJzdGF0cyIsInNldFN0YXRzIiwidG90YWxQcm9qZWN0cyIsImFjdGl2ZVByb2plY3RzIiwiY29tcGxldGVkUHJvamVjdHMiLCJ0b3RhbENsaWVudHMiLCJwZW5kaW5nUmVzcG9uc2VzIiwiY29tcGxldGlvblJhdGUiLCJhdmdDb21wbGV0aW9uVGltZSIsInByb2plY3RzIiwic2V0UHJvamVjdHMiLCJ0ZW1wbGF0ZXMiLCJzZXRUZW1wbGF0ZXMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJsb2FkRGFzaGJvYXJkRGF0YSIsInByb2plY3RzUmVzcG9uc2UiLCJmZXRjaCIsIm9rIiwicHJvamVjdHNEYXRhIiwianNvbiIsInRvdGFsIiwibGVuZ3RoIiwiYWN0aXZlIiwiZmlsdGVyIiwicCIsInN0YXR1cyIsImNvbXBsZXRlZCIsInBlbmRpbmciLCJNYXRoIiwicm91bmQiLCJpZCIsIm5hbWUiLCJjYXRlZ29yeSIsImVyciIsImNvbnNvbGUiLCJoYW5kbGVQcm9qZWN0U3RhdHVzQ2hhbmdlIiwicHJvamVjdCIsImxvZyIsImhhbmRsZVByb2plY3REZWxldGUiLCJjb25maXJtIiwicmVzcG9uc2UiLCJtZXRob2QiLCJkaXYiLCJjbGFzc05hbWUiLCJzaXplIiwiaDEiLCJ1c2VyIiwidmFyaWFudCIsInNwYW4iLCJyZWNlbnRQcm9qZWN0cyIsInNsaWNlIiwib25FZGl0IiwicHVzaCIsIm9uRGVsZXRlIiwib25WaWV3Iiwic2hhcmVUb2tlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/(dashboard)/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/projects-list.tsx":
/*!************************************************!*\
  !*** ./components/dashboard/projects-list.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectsList: () => (/* binding */ ProjectsList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_empty_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/empty-state */ \"(ssr)/./components/ui/empty-state.tsx\");\n// components/dashboard/projects-list.tsx\n/* __next_internal_client_entry_do_not_use__ ProjectsList auto */ \n\n\n\n\n\n\nfunction ProjectsList({ projects, onEdit, onDelete, onView }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const getStatusBadge = (status)=>{\n        switch(status){\n            case \"draft\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                    variant: \"secondary\",\n                    children: \"Draft\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 16\n                }, this);\n            case \"active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"Active\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 16\n                }, this);\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                    className: \"bg-blue-100 text-blue-800\",\n                    children: \"Completed\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                    variant: \"secondary\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (projects.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_empty_state__WEBPACK_IMPORTED_MODULE_5__.EmptyState, {\n            title: \"No projects yet\",\n            description: \"Create your first client onboarding project to get started.\",\n            action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                onClick: ()=>router.push(\"/projects/new\"),\n                children: \"Create Project\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                lineNumber: 51,\n                columnNumber: 11\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n        children: projects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                className: \"hover:shadow-lg transition-shadow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                            className: \"text-lg line-clamp-1\",\n                                            children: project.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardDescription, {\n                                            className: \"line-clamp-2 mt-1\",\n                                            children: project.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this),\n                                getStatusBadge(project.status)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"Clients: \",\n                                            project.clientCount\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"Created: \",\n                                            new Date(project.createdAt).toLocaleDateString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"Updated: \",\n                                            new Date(project.updatedAt).toLocaleDateString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>onView?.(project),\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"View\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>onEdit?.(project),\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Edit\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>onDelete?.(project),\n                                        className: \"text-red-600 hover:text-red-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, project.id, true, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/projects-list.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/projects-list.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/quick-actions.tsx":
/*!************************************************!*\
  !*** ./components/dashboard/quick-actions.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuickActions: () => (/* binding */ QuickActions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_FileText_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Plus,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Plus,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Plus,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Plus,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n// components/dashboard/quick-actions.tsx\n/* __next_internal_client_entry_do_not_use__ QuickActions auto */ \n\n\n\n\nfunction QuickActions({ recentProjects = [], templates = [] }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const actions = [\n        {\n            title: \"Create New Project\",\n            description: \"Start a new client onboarding project\",\n            icon: _barrel_optimize_names_FileText_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            action: ()=>router.push(\"/projects/new\"),\n            color: \"bg-blue-600 hover:bg-blue-700\"\n        },\n        {\n            title: \"Browse Templates\",\n            description: \"Explore our library of templates\",\n            icon: _barrel_optimize_names_FileText_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            action: ()=>router.push(\"/templates\"),\n            color: \"bg-green-600 hover:bg-green-700\"\n        },\n        {\n            title: \"Manage Clients\",\n            description: \"View and manage your clients\",\n            icon: _barrel_optimize_names_FileText_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            action: ()=>router.push(\"/clients\"),\n            color: \"bg-purple-600 hover:bg-purple-700\"\n        },\n        {\n            title: \"Settings\",\n            description: \"Configure your account settings\",\n            icon: _barrel_optimize_names_FileText_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            action: ()=>router.push(\"/settings\"),\n            color: \"bg-gray-600 hover:bg-gray-700\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n        children: actions.map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                className: \"hover:shadow-md transition-shadow cursor-pointer\",\n                onClick: action.action,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                        className: \"pb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `w-10 h-10 rounded-lg ${action.color} flex items-center justify-center mb-2`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(action.icon, {\n                                    className: \"h-5 w-5 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/quick-actions.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/quick-actions.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                className: \"text-lg\",\n                                children: action.title\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/quick-actions.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardDescription, {\n                                className: \"text-sm\",\n                                children: action.description\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/quick-actions.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/quick-actions.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                        className: \"pt-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            className: \"w-full\",\n                            children: \"Get Started\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/quick-actions.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/quick-actions.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, action.title, true, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/quick-actions.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/quick-actions.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/quick-actions.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/stats-cards.tsx":
/*!**********************************************!*\
  !*** ./components/dashboard/stats-cards.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatsCards: () => (/* binding */ StatsCards)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n// components/dashboard/stats-cards.tsx\n\n\n\nfunction StatsCards({ stats }) {\n    const cards = [\n        {\n            title: \"Total Projects\",\n            value: stats.totalProjects,\n            icon: _barrel_optimize_names_CheckCircle_Clock_FileText_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            color: \"text-blue-600\"\n        },\n        {\n            title: \"Active Projects\",\n            value: stats.activeProjects,\n            icon: _barrel_optimize_names_CheckCircle_Clock_FileText_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: \"text-orange-600\"\n        },\n        {\n            title: \"Completed\",\n            value: stats.completedProjects,\n            icon: _barrel_optimize_names_CheckCircle_Clock_FileText_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: \"text-green-600\"\n        },\n        {\n            title: \"Total Clients\",\n            value: stats.totalClients,\n            icon: _barrel_optimize_names_CheckCircle_Clock_FileText_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"text-purple-600\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n        children: cards.map((card)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                className: \"text-sm font-medium text-gray-600\",\n                                children: card.title\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/stats-cards.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(card.icon, {\n                                className: `h-4 w-4 ${card.color}`\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/stats-cards.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/stats-cards.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-2xl font-bold\",\n                            children: card.value\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/stats-cards.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/stats-cards.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, card.title, true, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/stats-cards.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/dashboard/stats-cards.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/stats-cards.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/header.tsx":
/*!**************************************!*\
  !*** ./components/layout/header.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(ssr)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(ssr)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,LogOut,Menu,Plus,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,LogOut,Menu,Plus,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,LogOut,Menu,Plus,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,LogOut,Menu,Plus,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,LogOut,Menu,Plus,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,LogOut,Menu,Plus,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,LogOut,Menu,Plus,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n// components/layout/header.tsx\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\n\n\n\n\nfunction Header({ onMenuToggle }) {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toggleMobileMenu = ()=>{\n        setIsMobileMenuOpen(!isMobileMenuOpen);\n        onMenuToggle?.();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white border-b border-gray-200 sticky top-0 z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleMobileMenu,\n                                    className: \"lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                    children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 35\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 53\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/dashboard\",\n                                    className: \"flex items-center space-x-2 ml-2 lg:ml-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-sm\",\n                                                children: \"CA\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-gray-900 hidden sm:block\",\n                                            children: \"Client Assistant\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden lg:flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/dashboard\",\n                                    className: \"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/projects\",\n                                    className: \"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\",\n                                    children: \"Projects\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/templates\",\n                                    className: \"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\",\n                                    children: \"Templates\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/projects/new\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        size: \"sm\",\n                                        className: \"hidden sm:flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                size: 16,\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"New Project\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                session?.user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                className: \"relative h-8 w-8 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                    className: \"h-8 w-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                            src: session.user.image || \"\",\n                                                            alt: session.user.name || \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                            children: session.user.name?.charAt(0) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                                lineNumber: 93,\n                                                                columnNumber: 58\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                            lineNumber: 92,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuContent, {\n                                            className: \"w-56\",\n                                            align: \"end\",\n                                            forceMount: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuLabel, {\n                                                    className: \"font-normal\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium leading-none\",\n                                                                children: session.user.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                                lineNumber: 101,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs leading-none text-muted-foreground\",\n                                                                children: session.user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                                lineNumber: 102,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"w-fit mt-1\",\n                                                                children: session.user.subscriptionTier || \"FREE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                                lineNumber: 105,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuSeparator, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/settings\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                                lineNumber: 113,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Settings\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/billing\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                                lineNumber: 119,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Billing\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuSeparator, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                                    onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.signOut)({\n                                                            callbackUrl: \"/\"\n                                                        }),\n                                                    className: \"text-red-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Sign out\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden bg-white border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-2 pt-2 pb-3 space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/dashboard\",\n                            className: \"block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50\",\n                            children: \"Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/projects\",\n                            className: \"block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50\",\n                            children: \"Projects\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/templates\",\n                            className: \"block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50\",\n                            children: \"Templates\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/projects/new\",\n                            className: \"block px-3 py-2 rounded-md text-base font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    size: 16,\n                                    className: \"inline mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this),\n                                \"New Project\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n                lineNumber: 140,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/header.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2xheW91dC9oZWFkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSwrQkFBK0I7O0FBR0M7QUFDSjtBQUN5QjtBQUNOO0FBQzZCO0FBUXRDO0FBQ087QUFDbUM7QUFNekUsU0FBU3NCLE9BQU8sRUFBRUMsWUFBWSxFQUFlO0lBQ2xELE1BQU0sRUFBRUMsTUFBTUMsT0FBTyxFQUFFLEdBQUd2QiwyREFBVUE7SUFDcEMsTUFBTSxDQUFDd0Isa0JBQWtCQyxvQkFBb0IsR0FBRzNCLCtDQUFRQSxDQUFDO0lBRXpELE1BQU00QixtQkFBbUI7UUFDdkJELG9CQUFvQixDQUFDRDtRQUNyQkg7SUFDRjtJQUVBLHFCQUNFLDhEQUFDTTtRQUFPQyxXQUFVOzswQkFDaEIsOERBQUNDO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDQztvQkFBSUQsV0FBVTs7c0NBRWIsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ0U7b0NBQ0NDLFNBQVNMO29DQUNURSxXQUFVOzhDQUVUSixpQ0FBbUIsOERBQUNWLHVIQUFDQTt3Q0FBQ2tCLE1BQU07Ozs7OzZEQUFTLDhEQUFDbkIsdUhBQUlBO3dDQUFDbUIsTUFBTTs7Ozs7Ozs7Ozs7OENBRXBELDhEQUFDakMsaURBQUlBO29DQUFDa0MsTUFBSztvQ0FBYUwsV0FBVTs7c0RBQ2hDLDhEQUFDQzs0Q0FBSUQsV0FBVTtzREFDYiw0RUFBQ007Z0RBQUtOLFdBQVU7MERBQStCOzs7Ozs7Ozs7OztzREFFakQsOERBQUNNOzRDQUFLTixXQUFVO3NEQUFrRDs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU90RSw4REFBQ087NEJBQUlQLFdBQVU7OzhDQUNiLDhEQUFDN0IsaURBQUlBO29DQUNIa0MsTUFBSztvQ0FDTEwsV0FBVTs4Q0FDWDs7Ozs7OzhDQUdELDhEQUFDN0IsaURBQUlBO29DQUNIa0MsTUFBSztvQ0FDTEwsV0FBVTs4Q0FDWDs7Ozs7OzhDQUdELDhEQUFDN0IsaURBQUlBO29DQUNIa0MsTUFBSztvQ0FDTEwsV0FBVTs4Q0FDWDs7Ozs7Ozs7Ozs7O3NDQU1ILDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUM3QixpREFBSUE7b0NBQUNrQyxNQUFLOzhDQUNULDRFQUFDL0IseURBQU1BO3dDQUFDOEIsTUFBSzt3Q0FBS0osV0FBVTs7MERBQzFCLDhEQUFDVCx3SEFBSUE7Z0RBQUNhLE1BQU07Z0RBQUlKLFdBQVU7Ozs7Ozs0Q0FBUzs7Ozs7Ozs7Ozs7O2dDQUt0Q0wsU0FBU2Esc0JBQ1IsOERBQUM5QixzRUFBWUE7O3NEQUNYLDhEQUFDSyw2RUFBbUJBOzRDQUFDMEIsT0FBTztzREFDMUIsNEVBQUNuQyx5REFBTUE7Z0RBQUNvQyxTQUFRO2dEQUFRVixXQUFVOzBEQUNoQyw0RUFBQ3pCLHlEQUFNQTtvREFBQ3lCLFdBQVU7O3NFQUNoQiw4REFBQ3ZCLDhEQUFXQTs0REFBQ2tDLEtBQUtoQixRQUFRYSxJQUFJLENBQUNJLEtBQUssSUFBSTs0REFBSUMsS0FBS2xCLFFBQVFhLElBQUksQ0FBQ00sSUFBSSxJQUFJOzs7Ozs7c0VBQ3RFLDhEQUFDdEMsaUVBQWNBO3NFQUNabUIsUUFBUWEsSUFBSSxDQUFDTSxJQUFJLEVBQUVDLE9BQU8sb0JBQU0sOERBQUM1Qix3SEFBSUE7Z0VBQUNpQixNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBS3JELDhEQUFDekIsNkVBQW1CQTs0Q0FBQ3FCLFdBQVU7NENBQU9nQixPQUFNOzRDQUFNQyxVQUFVOzs4REFDMUQsOERBQUNwQywyRUFBaUJBO29EQUFDbUIsV0FBVTs4REFDM0IsNEVBQUNDO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQ2tCO2dFQUFFbEIsV0FBVTswRUFBb0NMLFFBQVFhLElBQUksQ0FBQ00sSUFBSTs7Ozs7OzBFQUNsRSw4REFBQ0k7Z0VBQUVsQixXQUFVOzBFQUNWTCxRQUFRYSxJQUFJLENBQUNXLEtBQUs7Ozs7OzswRUFFckIsOERBQUNuQyx1REFBS0E7Z0VBQUMwQixTQUFRO2dFQUFZVixXQUFVOzBFQUNsQ0wsUUFBUWEsSUFBSSxDQUFDWSxnQkFBZ0IsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBSXhDLDhEQUFDdEMsK0VBQXFCQTs7Ozs7OERBQ3RCLDhEQUFDRiwwRUFBZ0JBO29EQUFDNkIsT0FBTzs4REFDdkIsNEVBQUN0QyxpREFBSUE7d0RBQUNrQyxNQUFLOzswRUFDVCw4REFBQ2pCLHdIQUFRQTtnRUFBQ1ksV0FBVTs7Ozs7OzREQUFpQjs7Ozs7Ozs7Ozs7OzhEQUl6Qyw4REFBQ3BCLDBFQUFnQkE7b0RBQUM2QixPQUFPOzhEQUN2Qiw0RUFBQ3RDLGlEQUFJQTt3REFBQ2tDLE1BQUs7OzBFQUNULDhEQUFDaEIsd0hBQVVBO2dFQUFDVyxXQUFVOzs7Ozs7NERBQWlCOzs7Ozs7Ozs7Ozs7OERBSTNDLDhEQUFDbEIsK0VBQXFCQTs7Ozs7OERBQ3RCLDhEQUFDRiwwRUFBZ0JBO29EQUNmdUIsU0FBUyxJQUFNOUIsd0RBQU9BLENBQUM7NERBQUVnRCxhQUFhO3dEQUFJO29EQUMxQ3JCLFdBQVU7O3NFQUVWLDhEQUFDVix3SEFBTUE7NERBQUNVLFdBQVU7Ozs7Ozt3REFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVdoREosa0NBQ0MsOERBQUNLO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDQztvQkFBSUQsV0FBVTs7c0NBQ2IsOERBQUM3QixpREFBSUE7NEJBQ0hrQyxNQUFLOzRCQUNMTCxXQUFVO3NDQUNYOzs7Ozs7c0NBR0QsOERBQUM3QixpREFBSUE7NEJBQ0hrQyxNQUFLOzRCQUNMTCxXQUFVO3NDQUNYOzs7Ozs7c0NBR0QsOERBQUM3QixpREFBSUE7NEJBQ0hrQyxNQUFLOzRCQUNMTCxXQUFVO3NDQUNYOzs7Ozs7c0NBR0QsOERBQUM3QixpREFBSUE7NEJBQ0hrQyxNQUFLOzRCQUNMTCxXQUFVOzs4Q0FFViw4REFBQ1Qsd0hBQUlBO29DQUFDYSxNQUFNO29DQUFJSixXQUFVOzs7Ozs7Z0NBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFReEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jbGllbnQtb25ib2FyZGluZy1hc3Npc3RhbnQvLi9jb21wb25lbnRzL2xheW91dC9oZWFkZXIudHN4P2IzNjQiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gY29tcG9uZW50cy9sYXlvdXQvaGVhZGVyLnRzeFxuJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnXG5pbXBvcnQgeyB1c2VTZXNzaW9uLCBzaWduT3V0IH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0J1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbidcbmltcG9ydCB7IEF2YXRhciwgQXZhdGFyRmFsbGJhY2ssIEF2YXRhckltYWdlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2F2YXRhcidcbmltcG9ydCB7XG4gIERyb3Bkb3duTWVudSxcbiAgRHJvcGRvd25NZW51Q29udGVudCxcbiAgRHJvcGRvd25NZW51SXRlbSxcbiAgRHJvcGRvd25NZW51TGFiZWwsXG4gIERyb3Bkb3duTWVudVNlcGFyYXRvcixcbiAgRHJvcGRvd25NZW51VHJpZ2dlcixcbn0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2Ryb3Bkb3duLW1lbnUnXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9iYWRnZSdcbmltcG9ydCB7IE1lbnUsIFgsIFVzZXIsIFNldHRpbmdzLCBDcmVkaXRDYXJkLCBMb2dPdXQsIFBsdXMgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5cbmludGVyZmFjZSBIZWFkZXJQcm9wcyB7XG4gIG9uTWVudVRvZ2dsZT86ICgpID0+IHZvaWRcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIEhlYWRlcih7IG9uTWVudVRvZ2dsZSB9OiBIZWFkZXJQcm9wcykge1xuICBjb25zdCB7IGRhdGE6IHNlc3Npb24gfSA9IHVzZVNlc3Npb24oKVxuICBjb25zdCBbaXNNb2JpbGVNZW51T3Blbiwgc2V0SXNNb2JpbGVNZW51T3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcblxuICBjb25zdCB0b2dnbGVNb2JpbGVNZW51ID0gKCkgPT4ge1xuICAgIHNldElzTW9iaWxlTWVudU9wZW4oIWlzTW9iaWxlTWVudU9wZW4pXG4gICAgb25NZW51VG9nZ2xlPy4oKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImJnLXdoaXRlIGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCBzdGlja3kgdG9wLTAgei01MFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBoLTE2XCI+XG4gICAgICAgICAgey8qIExvZ28gYW5kIEJyYW5kICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17dG9nZ2xlTW9iaWxlTWVudX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibGc6aGlkZGVuIHAtMiByb3VuZGVkLW1kIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1ncmF5LTkwMCBob3ZlcjpiZy1ncmF5LTEwMFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtpc01vYmlsZU1lbnVPcGVuID8gPFggc2l6ZT17MjB9IC8+IDogPE1lbnUgc2l6ZT17MjB9IC8+fVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8TGluayBocmVmPVwiL2Rhc2hib2FyZFwiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBtbC0yIGxnOm1sLTBcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLWJsdWUtNjAwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZCB0ZXh0LXNtXCI+Q0E8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIGhpZGRlbiBzbTpibG9ja1wiPlxuICAgICAgICAgICAgICAgIENsaWVudCBBc3Npc3RhbnRcbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIE5hdmlnYXRpb24gKi99XG4gICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJoaWRkZW4gbGc6ZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC04XCI+XG4gICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICBocmVmPVwiL2Rhc2hib2FyZFwiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgaG92ZXI6dGV4dC1ncmF5LTkwMCBweC0zIHB5LTIgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgRGFzaGJvYXJkXG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICBocmVmPVwiL3Byb2plY3RzXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIHB4LTMgcHktMiByb3VuZGVkLW1kIHRleHQtc20gZm9udC1tZWRpdW1cIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBQcm9qZWN0c1xuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgaHJlZj1cIi90ZW1wbGF0ZXNcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwIGhvdmVyOnRleHQtZ3JheS05MDAgcHgtMyBweS0yIHJvdW5kZWQtbWQgdGV4dC1zbSBmb250LW1lZGl1bVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIFRlbXBsYXRlc1xuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgIDwvbmF2PlxuXG4gICAgICAgICAgey8qIEFjdGlvbnMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvcHJvamVjdHMvbmV3XCI+XG4gICAgICAgICAgICAgIDxCdXR0b24gc2l6ZT1cInNtXCIgY2xhc3NOYW1lPVwiaGlkZGVuIHNtOmZsZXhcIj5cbiAgICAgICAgICAgICAgICA8UGx1cyBzaXplPXsxNn0gY2xhc3NOYW1lPVwibXItMlwiIC8+XG4gICAgICAgICAgICAgICAgTmV3IFByb2plY3RcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L0xpbms+XG5cbiAgICAgICAgICAgIHtzZXNzaW9uPy51c2VyICYmIChcbiAgICAgICAgICAgICAgPERyb3Bkb3duTWVudT5cbiAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51VHJpZ2dlciBhc0NoaWxkPlxuICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBoLTggdy04IHJvdW5kZWQtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICA8QXZhdGFyIGNsYXNzTmFtZT1cImgtOCB3LThcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8QXZhdGFySW1hZ2Ugc3JjPXtzZXNzaW9uLnVzZXIuaW1hZ2UgfHwgJyd9IGFsdD17c2Vzc2lvbi51c2VyLm5hbWUgfHwgJyd9IC8+XG4gICAgICAgICAgICAgICAgICAgICAgPEF2YXRhckZhbGxiYWNrPlxuICAgICAgICAgICAgICAgICAgICAgICAge3Nlc3Npb24udXNlci5uYW1lPy5jaGFyQXQoMCkgfHwgPFVzZXIgc2l6ZT17MTZ9IC8+fVxuICAgICAgICAgICAgICAgICAgICAgIDwvQXZhdGFyRmFsbGJhY2s+XG4gICAgICAgICAgICAgICAgICAgIDwvQXZhdGFyPlxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVUcmlnZ2VyPlxuICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVDb250ZW50IGNsYXNzTmFtZT1cInctNTZcIiBhbGlnbj1cImVuZFwiIGZvcmNlTW91bnQ+XG4gICAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51TGFiZWwgY2xhc3NOYW1lPVwiZm9udC1ub3JtYWxcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gbGVhZGluZy1ub25lXCI+e3Nlc3Npb24udXNlci5uYW1lfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIGxlYWRpbmctbm9uZSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtzZXNzaW9uLnVzZXIuZW1haWx9XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwic2Vjb25kYXJ5XCIgY2xhc3NOYW1lPVwidy1maXQgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge3Nlc3Npb24udXNlci5zdWJzY3JpcHRpb25UaWVyIHx8ICdGUkVFJ31cbiAgICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51TGFiZWw+XG4gICAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51U2VwYXJhdG9yIC8+XG4gICAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51SXRlbSBhc0NoaWxkPlxuICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3NldHRpbmdzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFNldHRpbmdzIGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgU2V0dGluZ3NcbiAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVJdGVtPlxuICAgICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudUl0ZW0gYXNDaGlsZD5cbiAgICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9iaWxsaW5nXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPENyZWRpdENhcmQgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICBCaWxsaW5nXG4gICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51SXRlbT5cbiAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVTZXBhcmF0b3IgLz5cbiAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVJdGVtXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNpZ25PdXQoeyBjYWxsYmFja1VybDogJy8nIH0pfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8TG9nT3V0IGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIFNpZ24gb3V0XG4gICAgICAgICAgICAgICAgICA8L0Ryb3Bkb3duTWVudUl0ZW0+XG4gICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVDb250ZW50PlxuICAgICAgICAgICAgICA8L0Ryb3Bkb3duTWVudT5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBNb2JpbGUgTmF2aWdhdGlvbiAqL31cbiAgICAgIHtpc01vYmlsZU1lbnVPcGVuICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpoaWRkZW4gYmctd2hpdGUgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC0yIHB0LTIgcGItMyBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgIGhyZWY9XCIvZGFzaGJvYXJkXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgcHgtMyBweS0yIHJvdW5kZWQtbWQgdGV4dC1iYXNlIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgaG92ZXI6dGV4dC1ncmF5LTkwMCBob3ZlcjpiZy1ncmF5LTUwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgRGFzaGJvYXJkXG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICBocmVmPVwiL3Byb2plY3RzXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgcHgtMyBweS0yIHJvdW5kZWQtbWQgdGV4dC1iYXNlIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgaG92ZXI6dGV4dC1ncmF5LTkwMCBob3ZlcjpiZy1ncmF5LTUwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgUHJvamVjdHNcbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgIGhyZWY9XCIvdGVtcGxhdGVzXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgcHgtMyBweS0yIHJvdW5kZWQtbWQgdGV4dC1iYXNlIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgaG92ZXI6dGV4dC1ncmF5LTkwMCBob3ZlcjpiZy1ncmF5LTUwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgVGVtcGxhdGVzXG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICBocmVmPVwiL3Byb2plY3RzL25ld1wiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHB4LTMgcHktMiByb3VuZGVkLW1kIHRleHQtYmFzZSBmb250LW1lZGl1bSB0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS03MDAgaG92ZXI6YmctYmx1ZS01MFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxQbHVzIHNpemU9ezE2fSBjbGFzc05hbWU9XCJpbmxpbmUgbXItMlwiIC8+XG4gICAgICAgICAgICAgIE5ldyBQcm9qZWN0XG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8L2hlYWRlcj5cbiAgKVxufSJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsIkxpbmsiLCJ1c2VTZXNzaW9uIiwic2lnbk91dCIsIkJ1dHRvbiIsIkF2YXRhciIsIkF2YXRhckZhbGxiYWNrIiwiQXZhdGFySW1hZ2UiLCJEcm9wZG93bk1lbnUiLCJEcm9wZG93bk1lbnVDb250ZW50IiwiRHJvcGRvd25NZW51SXRlbSIsIkRyb3Bkb3duTWVudUxhYmVsIiwiRHJvcGRvd25NZW51U2VwYXJhdG9yIiwiRHJvcGRvd25NZW51VHJpZ2dlciIsIkJhZGdlIiwiTWVudSIsIlgiLCJVc2VyIiwiU2V0dGluZ3MiLCJDcmVkaXRDYXJkIiwiTG9nT3V0IiwiUGx1cyIsIkhlYWRlciIsIm9uTWVudVRvZ2dsZSIsImRhdGEiLCJzZXNzaW9uIiwiaXNNb2JpbGVNZW51T3BlbiIsInNldElzTW9iaWxlTWVudU9wZW4iLCJ0b2dnbGVNb2JpbGVNZW51IiwiaGVhZGVyIiwiY2xhc3NOYW1lIiwiZGl2IiwiYnV0dG9uIiwib25DbGljayIsInNpemUiLCJocmVmIiwic3BhbiIsIm5hdiIsInVzZXIiLCJhc0NoaWxkIiwidmFyaWFudCIsInNyYyIsImltYWdlIiwiYWx0IiwibmFtZSIsImNoYXJBdCIsImFsaWduIiwiZm9yY2VNb3VudCIsInAiLCJlbWFpbCIsInN1YnNjcmlwdGlvblRpZXIiLCJjYWxsYmFja1VybCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/main-layout.tsx":
/*!*******************************************!*\
  !*** ./components/layout/main-layout.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MainLayout: () => (/* binding */ MainLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./header */ \"(ssr)/./components/layout/header.tsx\");\n/* harmony import */ var _sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./sidebar */ \"(ssr)/./components/layout/sidebar.tsx\");\n/* harmony import */ var _components_ui_loading_spinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/loading-spinner */ \"(ssr)/./components/ui/loading-spinner.tsx\");\n// components/layout/main-layout.tsx\n/* __next_internal_client_entry_do_not_use__ MainLayout auto */ \n\n\n\n\n\nfunction MainLayout({ children }) {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loading_spinner__WEBPACK_IMPORTED_MODULE_5__.LoadingSpinner, {\n                size: \"lg\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/main-layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/main-layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session) {\n        return null // Will be handled by middleware\n        ;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_header__WEBPACK_IMPORTED_MODULE_3__.Header, {\n                onMenuToggle: ()=>setSidebarCollapsed(!sidebarCollapsed)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/main-layout.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sidebar__WEBPACK_IMPORTED_MODULE_4__.Sidebar, {\n                            isCollapsed: sidebarCollapsed,\n                            onToggle: ()=>setSidebarCollapsed(!sidebarCollapsed)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/main-layout.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/main-layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 lg:pl-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-6 px-4 sm:px-6 lg:px-8\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/main-layout.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/main-layout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/main-layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/main-layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/main-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/sidebar.tsx":
/*!***************************************!*\
  !*** ./components/layout/sidebar.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(ssr)/./components/ui/separator.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,CreditCard,FileText,FolderOpen,HelpCircle,LayoutDashboard,Plus,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,CreditCard,FileText,FolderOpen,HelpCircle,LayoutDashboard,Plus,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,CreditCard,FileText,FolderOpen,HelpCircle,LayoutDashboard,Plus,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,CreditCard,FileText,FolderOpen,HelpCircle,LayoutDashboard,Plus,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,CreditCard,FileText,FolderOpen,HelpCircle,LayoutDashboard,Plus,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,CreditCard,FileText,FolderOpen,HelpCircle,LayoutDashboard,Plus,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,CreditCard,FileText,FolderOpen,HelpCircle,LayoutDashboard,Plus,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,CreditCard,FileText,FolderOpen,HelpCircle,LayoutDashboard,Plus,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/help-circle.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,CreditCard,FileText,FolderOpen,HelpCircle,LayoutDashboard,Plus,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,CreditCard,FileText,FolderOpen,HelpCircle,LayoutDashboard,Plus,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,CreditCard,FileText,FolderOpen,HelpCircle,LayoutDashboard,Plus,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n// components/layout/sidebar.tsx\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Projects\",\n        href: \"/projects\",\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: \"Templates\",\n        href: \"/templates\",\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: \"Analytics\",\n        href: \"/analytics\",\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        badge: \"Pro\"\n    },\n    {\n        name: \"Team\",\n        href: \"/team\",\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        badge: \"Business\"\n    }\n];\nconst bottomNavigation = [\n    {\n        name: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        name: \"Billing\",\n        href: \"/billing\",\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    {\n        name: \"Help\",\n        href: \"/help\",\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    }\n];\nfunction Sidebar({ className, isCollapsed = false, onToggle }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex flex-col h-full bg-white border-r border-gray-200 transition-all duration-300\", isCollapsed ? \"w-16\" : \"w-64\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                children: [\n                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white font-bold text-sm\",\n                                    children: \"CA\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-semibold text-gray-900\",\n                                children: \"Client Assistant\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: onToggle,\n                        className: \"p-2\",\n                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 26\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 55\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    href: \"/projects/new\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        className: \"w-full\",\n                        size: isCollapsed ? \"sm\" : \"default\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_CreditCard_FileText_FolderOpen_HelpCircle_LayoutDashboard_Plus_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                size: 16,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"\", !isCollapsed && \"mr-2\")\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this),\n                            !isCollapsed && \"New Project\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 px-2 py-4 space-y-1\",\n                children: navigation.map((item)=>{\n                    const isActive = pathname === item.href;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        href: item.href,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors\", isActive ? \"bg-blue-50 text-blue-700 border-r-2 border-blue-700\" : \"text-gray-700 hover:text-gray-900 hover:bg-gray-50\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex-shrink-0 h-5 w-5\", isActive ? \"text-blue-700\" : \"text-gray-400 group-hover:text-gray-500\", !isCollapsed && \"mr-3\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 17\n                                }, this),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex-1\",\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 21\n                                        }, this),\n                                        item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"ml-2 text-xs\",\n                                            children: item.badge\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 15\n                        }, this)\n                    }, item.name, false, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {\n                        className: \"mb-2\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    bottomNavigation.map((item)=>{\n                        const isActive = pathname === item.href;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: item.href,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors\", isActive ? \"bg-gray-100 text-gray-900\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-50\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex-shrink-0 h-5 w-5\", isActive ? \"text-gray-700\" : \"text-gray-400 group-hover:text-gray-500\", !isCollapsed && \"mr-3\")\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, this),\n                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 34\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 15\n                            }, this)\n                        }, item.name, false, {\n                            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this);\n                    })\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/sidebar.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/alert.tsx":
/*!*********************************!*\
  !*** ./components/ui/alert.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\", {\n    variants: {\n        variant: {\n            default: \"bg-background text-foreground\",\n            destructive: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Alert = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/alert.tsx\",\n        lineNumber: 22,\n        columnNumber: 46\n    }, undefined));\nAlert.displayName = \"Alert\";\nconst AlertTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mb-1 font-medium leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/alert.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined));\nAlertTitle.displayName = \"AlertTitle\";\nconst AlertDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/alert.tsx\",\n        lineNumber: 32,\n        columnNumber: 37\n    }, undefined));\nAlertDescription.displayName = \"AlertDescription\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/avatar.tsx":
/*!**********************************!*\
  !*** ./components/ui/avatar.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-avatar */ \"(ssr)/./node_modules/@radix-ui/react-avatar/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/avatar.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined));\nAvatar.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\nconst AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Image, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"aspect-square h-full w-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/avatar.tsx\",\n        lineNumber: 17,\n        columnNumber: 3\n    }, undefined));\nAvatarImage.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Image.displayName;\nconst AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Fallback, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-full w-full items-center justify-center rounded-full bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/avatar.tsx\",\n        lineNumber: 25,\n        columnNumber: 3\n    }, undefined));\nAvatarFallback.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Fallback.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/avatar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/badge.tsx\",\n        lineNumber: 26,\n        columnNumber: 10\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/button.tsx\",\n        lineNumber: 39,\n        columnNumber: 10\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/card.tsx\",\n        lineNumber: 6,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/card.tsx\",\n        lineNumber: 11,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/card.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/card.tsx\",\n        lineNumber: 21,\n        columnNumber: 37\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/card.tsx\",\n        lineNumber: 26,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/card.tsx\",\n        lineNumber: 31,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/dropdown-menu.tsx":
/*!*****************************************!*\
  !*** ./components/ui/dropdown-menu.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuShortcut: () => (/* binding */ DropdownMenuShortcut),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(ssr)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst DropdownMenu = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DropdownMenuTrigger = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DropdownMenuGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst DropdownMenuPortal = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DropdownMenuSub = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Sub;\nconst DropdownMenuRadioGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup;\nconst DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\", inset && \"pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"ml-auto h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/dropdown-menu.tsx\",\n                lineNumber: 35,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/dropdown-menu.tsx\",\n        lineNumber: 25,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubTrigger.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger.displayName;\nconst DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/dropdown-menu.tsx\",\n        lineNumber: 44,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent.displayName;\nconst DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/dropdown-menu.tsx\",\n            lineNumber: 60,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/dropdown-menu.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/dropdown-menu.tsx\",\n        lineNumber: 79,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, checked, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/dropdown-menu.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/dropdown-menu.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/dropdown-menu.tsx\",\n                lineNumber: 104,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/dropdown-menu.tsx\",\n        lineNumber: 95,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuCheckboxItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem.displayName;\nconst DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-2 w-2 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/dropdown-menu.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/dropdown-menu.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/dropdown-menu.tsx\",\n                lineNumber: 126,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/dropdown-menu.tsx\",\n        lineNumber: 118,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuRadioItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem.displayName;\nconst DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/dropdown-menu.tsx\",\n        lineNumber: 142,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuLabel.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/dropdown-menu.tsx\",\n        lineNumber: 150,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSeparator.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\nconst DropdownMenuShortcut = ({ className, ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"ml-auto text-xs tracking-widest opacity-60\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/dropdown-menu.tsx\",\n        lineNumber: 155,\n        columnNumber: 10\n    }, undefined);\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/dropdown-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/empty-state.tsx":
/*!***************************************!*\
  !*** ./components/ui/empty-state.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmptyState: () => (/* binding */ EmptyState)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n// components/ui/empty-state.tsx\n\nfunction EmptyState({ title, description, icon, action }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-center py-12\",\n        children: [\n            icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto h-12 w-12 text-gray-400 mb-4\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/empty-state.tsx\",\n                lineNumber: 15,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-2\",\n                children: title\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/empty-state.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 mb-6 max-w-sm mx-auto\",\n                children: description\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/empty-state.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this),\n            action && action\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/empty-state.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2VtcHR5LXN0YXRlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQSxnQ0FBZ0M7O0FBVXpCLFNBQVNBLFdBQVcsRUFBRUMsS0FBSyxFQUFFQyxXQUFXLEVBQUVDLElBQUksRUFBRUMsTUFBTSxFQUFtQjtJQUM5RSxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7WUFDWkgsc0JBQ0MsOERBQUNFO2dCQUFJQyxXQUFVOzBCQUNaSDs7Ozs7OzBCQUdMLDhEQUFDSTtnQkFBR0QsV0FBVTswQkFBMENMOzs7Ozs7WUFDdkRDLDZCQUNDLDhEQUFDTTtnQkFBRUYsV0FBVTswQkFBdUNKOzs7Ozs7WUFFckRFLFVBQVVBOzs7Ozs7O0FBR2pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2xpZW50LW9uYm9hcmRpbmctYXNzaXN0YW50Ly4vY29tcG9uZW50cy91aS9lbXB0eS1zdGF0ZS50c3g/MGFjMCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBjb21wb25lbnRzL3VpL2VtcHR5LXN0YXRlLnRzeFxuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5cbmludGVyZmFjZSBFbXB0eVN0YXRlUHJvcHMge1xuICB0aXRsZTogc3RyaW5nXG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nXG4gIGljb24/OiBSZWFjdE5vZGVcbiAgYWN0aW9uPzogUmVhY3ROb2RlXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBFbXB0eVN0YXRlKHsgdGl0bGUsIGRlc2NyaXB0aW9uLCBpY29uLCBhY3Rpb24gfTogRW1wdHlTdGF0ZVByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMlwiPlxuICAgICAge2ljb24gJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm14LWF1dG8gaC0xMiB3LTEyIHRleHQtZ3JheS00MDAgbWItNFwiPlxuICAgICAgICAgIHtpY29ufVxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj57dGl0bGV9PC9oMz5cbiAgICAgIHtkZXNjcmlwdGlvbiAmJiAoXG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNiBtYXgtdy1zbSBteC1hdXRvXCI+e2Rlc2NyaXB0aW9ufTwvcD5cbiAgICAgICl9XG4gICAgICB7YWN0aW9uICYmIGFjdGlvbn1cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkVtcHR5U3RhdGUiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiaWNvbiIsImFjdGlvbiIsImRpdiIsImNsYXNzTmFtZSIsImgzIiwicCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/empty-state.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/loading-spinner.tsx":
/*!*******************************************!*\
  !*** ./components/ui/loading-spinner.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingSpinner: () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n// components/ui/loading-spinner.tsx\n\n\nfunction LoadingSpinner({ size = \"md\", className }) {\n    const sizeClasses = {\n        sm: \"h-4 w-4\",\n        md: \"h-6 w-6\",\n        lg: \"h-8 w-8\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"animate-spin rounded-full border-2 border-gray-300 border-t-blue-600\", sizeClasses[size], className)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/loading-spinner.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2xvYWRpbmctc3Bpbm5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLG9DQUFvQzs7QUFDSjtBQU96QixTQUFTQyxlQUFlLEVBQUVDLE9BQU8sSUFBSSxFQUFFQyxTQUFTLEVBQXVCO0lBQzVFLE1BQU1DLGNBQWM7UUFDbEJDLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO0lBQ047SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUwsV0FBV0gsOENBQUVBLENBQUMsd0VBQXdFSSxXQUFXLENBQUNGLEtBQUssRUFBRUM7Ozs7OztBQUVsSCIsInNvdXJjZXMiOlsid2VicGFjazovL2NsaWVudC1vbmJvYXJkaW5nLWFzc2lzdGFudC8uL2NvbXBvbmVudHMvdWkvbG9hZGluZy1zcGlubmVyLnRzeD9iZjMyIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGNvbXBvbmVudHMvdWkvbG9hZGluZy1zcGlubmVyLnRzeFxuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscydcblxuaW50ZXJmYWNlIExvYWRpbmdTcGlubmVyUHJvcHMge1xuICBzaXplPzogJ3NtJyB8ICdtZCcgfCAnbGcnXG4gIGNsYXNzTmFtZT86IHN0cmluZ1xufVxuXG5leHBvcnQgZnVuY3Rpb24gTG9hZGluZ1NwaW5uZXIoeyBzaXplID0gJ21kJywgY2xhc3NOYW1lIH06IExvYWRpbmdTcGlubmVyUHJvcHMpIHtcbiAgY29uc3Qgc2l6ZUNsYXNzZXMgPSB7XG4gICAgc206ICdoLTQgdy00JyxcbiAgICBtZDogJ2gtNiB3LTYnLFxuICAgIGxnOiAnaC04IHctOCdcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2NuKCdhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGJvcmRlci0yIGJvcmRlci1ncmF5LTMwMCBib3JkZXItdC1ibHVlLTYwMCcsIHNpemVDbGFzc2VzW3NpemVdLCBjbGFzc05hbWUpfSAvPlxuICApXG59XG4iXSwibmFtZXMiOlsiY24iLCJMb2FkaW5nU3Bpbm5lciIsInNpemUiLCJjbGFzc05hbWUiLCJzaXplQ2xhc3NlcyIsInNtIiwibWQiLCJsZyIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/loading-spinner.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/separator.tsx":
/*!*************************************!*\
  !*** ./components/ui/separator.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Separator: () => (/* binding */ Separator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-separator */ \"(ssr)/./node_modules/@radix-ui/react-separator/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst Separator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, orientation = \"horizontal\", decorative = true, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        decorative: decorative,\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"shrink-0 bg-border\", orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/ui/separator.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined));\nSeparator.displayName = _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3NlcGFyYXRvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBK0I7QUFDaUM7QUFFL0I7QUFFakMsTUFBTUcsMEJBQVlILDZDQUFnQixDQUdoQyxDQUFDLEVBQUVLLFNBQVMsRUFBRUMsY0FBYyxZQUFZLEVBQUVDLGFBQWEsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQ3pFLDhEQUFDUiwyREFBdUI7UUFDdEJRLEtBQUtBO1FBQ0xGLFlBQVlBO1FBQ1pELGFBQWFBO1FBQ2JELFdBQVdILDhDQUFFQSxDQUFDLHNCQUFzQkksZ0JBQWdCLGVBQWUsbUJBQW1CLGtCQUFrQkQ7UUFDdkcsR0FBR0csS0FBSzs7Ozs7O0FBR2JMLFVBQVVRLFdBQVcsR0FBR1YsMkRBQXVCLENBQUNVLFdBQVc7QUFFdEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jbGllbnQtb25ib2FyZGluZy1hc3Npc3RhbnQvLi9jb21wb25lbnRzL3VpL3NlcGFyYXRvci50c3g/YjQxZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgKiBhcyBTZXBhcmF0b3JQcmltaXRpdmUgZnJvbSAnQHJhZGl4LXVpL3JlYWN0LXNlcGFyYXRvcic7XG5cbmltcG9ydCB7IGNuIH0gZnJvbSAnQC9saWIvdXRpbHMnO1xuXG5jb25zdCBTZXBhcmF0b3IgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBTZXBhcmF0b3JQcmltaXRpdmUuUm9vdD4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgU2VwYXJhdG9yUHJpbWl0aXZlLlJvb3Q+XG4+KCh7IGNsYXNzTmFtZSwgb3JpZW50YXRpb24gPSAnaG9yaXpvbnRhbCcsIGRlY29yYXRpdmUgPSB0cnVlLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPFNlcGFyYXRvclByaW1pdGl2ZS5Sb290XG4gICAgcmVmPXtyZWZ9XG4gICAgZGVjb3JhdGl2ZT17ZGVjb3JhdGl2ZX1cbiAgICBvcmllbnRhdGlvbj17b3JpZW50YXRpb259XG4gICAgY2xhc3NOYW1lPXtjbignc2hyaW5rLTAgYmctYm9yZGVyJywgb3JpZW50YXRpb24gPT09ICdob3Jpem9udGFsJyA/ICdoLVsxcHhdIHctZnVsbCcgOiAnaC1mdWxsIHctWzFweF0nLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpO1xuU2VwYXJhdG9yLmRpc3BsYXlOYW1lID0gU2VwYXJhdG9yUHJpbWl0aXZlLlJvb3QuZGlzcGxheU5hbWU7XG5cbmV4cG9ydCB7IFNlcGFyYXRvciB9O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiU2VwYXJhdG9yUHJpbWl0aXZlIiwiY24iLCJTZXBhcmF0b3IiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwib3JpZW50YXRpb24iLCJkZWNvcmF0aXZlIiwicHJvcHMiLCJyZWYiLCJSb290IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/separator.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   generateShareToken: () => (/* binding */ generateShareToken)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction generateShareToken() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE2QztBQUNKO0FBRWxDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QjtBQUVPLFNBQVNDO0lBQ2QsT0FBT0MsS0FBS0MsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsU0FBUyxDQUFDLEdBQUcsTUFBTUgsS0FBS0MsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsU0FBUyxDQUFDLEdBQUc7QUFDL0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jbGllbnQtb25ib2FyZGluZy1hc3Npc3RhbnQvLi9saWIvdXRpbHMudHM/Zjc0NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tICdjbHN4JztcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tICd0YWlsd2luZC1tZXJnZSc7XG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVTaGFyZVRva2VuKCk6IHN0cmluZyB7XG4gIHJldHVybiBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHJpbmcoMiwgMTUpICsgTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyaW5nKDIsIDE1KTtcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIiwiZ2VuZXJhdGVTaGFyZVRva2VuIiwiTWF0aCIsInJhbmRvbSIsInRvU3RyaW5nIiwic3Vic3RyaW5nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"06c97fc9051c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jbGllbnQtb25ib2FyZGluZy1hc3Npc3RhbnQvLi9hcHAvZ2xvYmFscy5jc3M/ZTBmNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjA2Yzk3ZmM5MDUxY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/(dashboard)/dashboard/page.tsx":
/*!********************************************!*\
  !*** ./app/(dashboard)/dashboard/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/dashboard/page.tsx#default`));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/(dashboard)/dashboard/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/(dashboard)/layout.tsx":
/*!************************************!*\
  !*** ./app/(dashboard)/layout.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(rsc)/./components/layout/main-layout.tsx\");\n// app/(dashboard)/layout.tsx\n\n\n\n\n\nasync function DashboardLayout({ children }) {\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_2__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authOptions);\n    // Redirect unauthenticated users to login\n    if (!session) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.redirect)(\"/login\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_4__.MainLayout, {\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/(dashboard)/layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvKGRhc2hib2FyZCkvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBLDZCQUE2Qjs7QUFDYTtBQUNFO0FBQ0o7QUFDb0I7QUFFN0MsZUFBZUksZ0JBQWdCLEVBQzVDQyxRQUFRLEVBR1Q7SUFDQyxNQUFNQyxVQUFVLE1BQU1MLDJEQUFnQkEsQ0FBQ0Msa0RBQVdBO0lBRWxELDBDQUEwQztJQUMxQyxJQUFJLENBQUNJLFNBQVM7UUFDWk4seURBQVFBLENBQUM7SUFDWDtJQUVBLHFCQUFPLDhEQUFDRyxzRUFBVUE7a0JBQUVFOzs7Ozs7QUFDdEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jbGllbnQtb25ib2FyZGluZy1hc3Npc3RhbnQvLi9hcHAvKGRhc2hib2FyZCkvbGF5b3V0LnRzeD8zYzg3Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIGFwcC8oZGFzaGJvYXJkKS9sYXlvdXQudHN4XG5pbXBvcnQgeyByZWRpcmVjdCB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcbmltcG9ydCB7IGdldFNlcnZlclNlc3Npb24gfSBmcm9tICduZXh0LWF1dGgnXG5pbXBvcnQgeyBhdXRoT3B0aW9ucyB9IGZyb20gJ0AvbGliL2F1dGgnXG5pbXBvcnQgeyBNYWluTGF5b3V0IH0gZnJvbSAnQC9jb21wb25lbnRzL2xheW91dC9tYWluLWxheW91dCdcblxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gRGFzaGJvYXJkTGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IGdldFNlcnZlclNlc3Npb24oYXV0aE9wdGlvbnMpXG5cbiAgLy8gUmVkaXJlY3QgdW5hdXRoZW50aWNhdGVkIHVzZXJzIHRvIGxvZ2luXG4gIGlmICghc2Vzc2lvbikge1xuICAgIHJlZGlyZWN0KCcvbG9naW4nKVxuICB9XG5cbiAgcmV0dXJuIDxNYWluTGF5b3V0PntjaGlsZHJlbn08L01haW5MYXlvdXQ+XG59Il0sIm5hbWVzIjpbInJlZGlyZWN0IiwiZ2V0U2VydmVyU2Vzc2lvbiIsImF1dGhPcHRpb25zIiwiTWFpbkxheW91dCIsIkRhc2hib2FyZExheW91dCIsImNoaWxkcmVuIiwic2Vzc2lvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/(dashboard)/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Client Onboarding Assistant\",\n    description: \"Professional client onboarding made simple\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/app/layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUlNQTtBQUZnQjtBQUlmLE1BQU1DLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdULDJKQUFlO3NCQUFHSzs7Ozs7Ozs7Ozs7QUFHekMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jbGllbnQtb25ib2FyZGluZy1hc3Npc3RhbnQvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnQ2xpZW50IE9uYm9hcmRpbmcgQXNzaXN0YW50JyxcbiAgZGVzY3JpcHRpb246ICdQcm9mZXNzaW9uYWwgY2xpZW50IG9uYm9hcmRpbmcgbWFkZSBzaW1wbGUnLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT57Y2hpbGRyZW59PC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/layout/main-layout.tsx":
/*!*******************************************!*\
  !*** ./components/layout/main-layout.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MainLayout: () => (/* binding */ e0)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/PROJETS/Web/client-onboarding-assistant/components/layout/main-layout.tsx#MainLayout`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/layout/main-layout.tsx\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/prisma-adapter */ \"(rsc)/./node_modules/@auth/prisma-adapter/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var next_auth_providers_linkedin__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/linkedin */ \"(rsc)/./node_modules/next-auth/providers/linkedin.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_6__);\n// lib/auth.ts\n\n\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_ID,\n            clientSecret: process.env.GITHUB_SECRET\n        }),\n        (0,next_auth_providers_linkedin__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            clientId: process.env.LINKEDIN_CLIENT_ID,\n            clientSecret: process.env.LINKEDIN_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user || !user.password) {\n                    return null;\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_6___default().compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    return null;\n                }\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    image: user.image\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/login\",\n        error: \"/login\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _prisma_extension_accelerate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/extension-accelerate */ \"(rsc)/./node_modules/@prisma/extension-accelerate/dist/index.js\");\n// lib/prisma.ts\n\n\nconst globalForPrisma = globalThis;\nconst prismaBase = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nconst prisma = prismaBase.$extends((0,_prisma_extension_accelerate__WEBPACK_IMPORTED_MODULE_1__.withAccelerate)());\nif (true) globalForPrisma.prisma = prisma;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQSxnQkFBZ0I7QUFDNkI7QUFDZ0I7QUFFN0QsTUFBTUUsa0JBQWtCQztBQUl4QixNQUFNQyxhQUFhLElBQUlKLHdEQUFZQTtBQUNuQyxNQUFNSyxTQUFTRCxXQUFXRSxRQUFRLENBQUNMLDRFQUFjQTtBQUVqRCxJQUFJTSxJQUF5QixFQUFjTCxnQkFBZ0JHLE1BQU0sR0FBR0E7QUFFbkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jbGllbnQtb25ib2FyZGluZy1hc3Npc3RhbnQvLi9saWIvcHJpc21hLnRzPzk4MjIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gbGliL3ByaXNtYS50c1xuaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5pbXBvcnQgeyB3aXRoQWNjZWxlcmF0ZSB9IGZyb20gJ0BwcmlzbWEvZXh0ZW5zaW9uLWFjY2VsZXJhdGUnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUmV0dXJuVHlwZTxQcmlzbWFDbGllbnRbJyRleHRlbmRzJ10+IHwgdW5kZWZpbmVkXG59XG5cbmNvbnN0IHByaXNtYUJhc2UgPSBuZXcgUHJpc21hQ2xpZW50KClcbmNvbnN0IHByaXNtYSA9IHByaXNtYUJhc2UuJGV4dGVuZHMod2l0aEFjY2VsZXJhdGUoKSlcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWFcblxuZXhwb3J0IHsgcHJpc21hIH1cbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJ3aXRoQWNjZWxlcmF0ZSIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWFCYXNlIiwicHJpc21hIiwiJGV4dGVuZHMiLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/@babel","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/@prisma","vendor-chunks/@auth","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/react-remove-scroll","vendor-chunks/@floating-ui","vendor-chunks/react-style-singleton","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/use-sync-external-store","vendor-chunks/use-sidecar","vendor-chunks/tslib","vendor-chunks/get-nonce","vendor-chunks/aria-hidden"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(dashboard)%2Fdashboard%2Fpage&page=%2F(dashboard)%2Fdashboard%2Fpage&appPaths=%2F(dashboard)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fasalako%2FDesktop%2FPROJETS%2FWeb%2Fclient-onboarding-assistant&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();