"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@auth";
exports.ids = ["vendor-chunks/@auth"];
exports.modules = {

/***/ "(rsc)/./node_modules/@auth/prisma-adapter/index.js":
/*!****************************************************!*\
  !*** ./node_modules/@auth/prisma-adapter/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PrismaAdapter: () => (/* binding */ PrismaAdapter)\n/* harmony export */ });\nfunction PrismaAdapter(prisma) {\n    const p = prisma;\n    return {\n        // We need to let Prisma generate the ID because our default UUID is incompatible with MongoDB\n        createUser: ({ id, ...data }) => p.user.create(stripUndefined(data)),\n        getUser: (id) => p.user.findUnique({ where: { id } }),\n        getUserByEmail: (email) => p.user.findUnique({ where: { email } }),\n        async getUserByAccount(provider_providerAccountId) {\n            const account = await p.account.findUnique({\n                where: { provider_providerAccountId },\n                include: { user: true },\n            });\n            return account?.user ?? null;\n        },\n        updateUser: ({ id, ...data }) => p.user.update({\n            where: { id },\n            ...stripUndefined(data),\n        }),\n        deleteUser: (id) => p.user.delete({ where: { id } }),\n        linkAccount: (data) => p.account.create({ data }),\n        unlinkAccount: (provider_providerAccountId) => p.account.delete({\n            where: { provider_providerAccountId },\n        }),\n        async getSessionAndUser(sessionToken) {\n            const userAndSession = await p.session.findUnique({\n                where: { sessionToken },\n                include: { user: true },\n            });\n            if (!userAndSession)\n                return null;\n            const { user, ...session } = userAndSession;\n            return { user, session };\n        },\n        createSession: (data) => p.session.create(stripUndefined(data)),\n        updateSession: (data) => p.session.update({\n            where: { sessionToken: data.sessionToken },\n            ...stripUndefined(data),\n        }),\n        deleteSession: (sessionToken) => p.session.delete({ where: { sessionToken } }),\n        async createVerificationToken(data) {\n            const verificationToken = await p.verificationToken.create(stripUndefined(data));\n            if (\"id\" in verificationToken && verificationToken.id)\n                delete verificationToken.id;\n            return verificationToken;\n        },\n        async useVerificationToken(identifier_token) {\n            try {\n                const verificationToken = await p.verificationToken.delete({\n                    where: { identifier_token },\n                });\n                if (\"id\" in verificationToken && verificationToken.id)\n                    delete verificationToken.id;\n                return verificationToken;\n            }\n            catch (error) {\n                // If token already used/deleted, just return null\n                // https://www.prisma.io/docs/reference/api-reference/error-reference#p2025\n                if (error &&\n                    typeof error === \"object\" &&\n                    \"code\" in error &&\n                    error.code === \"P2025\")\n                    return null;\n                throw error;\n            }\n        },\n        async getAccount(providerAccountId, provider) {\n            return p.account.findFirst({\n                where: { providerAccountId, provider },\n            });\n        },\n        async createAuthenticator(data) {\n            return p.authenticator.create(stripUndefined(data));\n        },\n        async getAuthenticator(credentialID) {\n            return p.authenticator.findUnique({\n                where: { credentialID },\n            });\n        },\n        async listAuthenticatorsByUserId(userId) {\n            return p.authenticator.findMany({\n                where: { userId },\n            });\n        },\n        async updateAuthenticatorCounter(credentialID, counter) {\n            return p.authenticator.update({\n                where: { credentialID },\n                data: { counter },\n            });\n        },\n    };\n}\n/** @see https://www.prisma.io/docs/orm/prisma-client/special-fields-and-types/null-and-undefined */\nfunction stripUndefined(obj) {\n    const data = {};\n    for (const key in obj)\n        if (obj[key] !== undefined)\n            data[key] = obj[key];\n    return { data };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth/prisma-adapter/index.js\n");

/***/ })

};
;